package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Represents a production log entry for tracking what was produced.
 * 
 * @param id Unique identifier for the production log
 * @param productId ID of the product that was produced
 * @param recipeId ID of the recipe used (optional)
 * @param quantityProduced Number of units produced
 * @param productionDate Date when production occurred
 * @param batchNumber Optional batch number for tracking
 * @param notes Optional notes about the production
 * @param staffMember Name or ID of staff member who did the production
 * @param costPerUnit Cost per unit at time of production
 * @param totalCost Total cost of production
 * @param createdAt Timestamp when log was created
 */
@Entity(
    tableName = "production_logs",
    foreignKeys = [
        ForeignKey(
            entity = Product::class,
            parentColumns = ["id"],
            childColumns = ["productId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = Recipe::class,
            parentColumns = ["id"],
            childColumns = ["recipeId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [
        Index(value = ["productId"]),
        Index(value = ["recipeId"]),
        Index(value = ["productionDate"]),
        Index(value = ["batchNumber"])
    ]
)
data class ProductionLog(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val productId: Long,
    val recipeId: Long? = null,
    val quantityProduced: Int,
    val productionDate: Long,
    val batchNumber: String? = null,
    val notes: String = "",
    val staffMember: String = "",
    val costPerUnit: Double = 0.0,
    val totalCost: Double = 0.0,
    val createdAt: Long = System.currentTimeMillis()
) {
    /**
     * Formats the production date as a readable string
     */
    fun getFormattedProductionDate(): String {
        val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
        return dateFormat.format(Date(productionDate))
    }
    
    /**
     * Formats the production date and time as a readable string
     */
    fun getFormattedProductionDateTime(): String {
        val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
        return dateFormat.format(Date(productionDate))
    }
    
    /**
     * Calculates total revenue if all units were sold at given price
     */
    fun calculatePotentialRevenue(sellingPrice: Double): Double {
        return quantityProduced * sellingPrice
    }
    
    /**
     * Calculates profit if all units were sold at given price
     */
    fun calculatePotentialProfit(sellingPrice: Double): Double {
        return calculatePotentialRevenue(sellingPrice) - totalCost
    }
}
