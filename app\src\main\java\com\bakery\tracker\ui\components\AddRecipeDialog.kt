package com.bakery.tracker.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.bakery.tracker.data.models.*
import com.bakery.tracker.ui.theme.BakeryTrackerTheme

/**
 * Dialog for adding new recipes
 * Implements recipe creation as described in overview.txt
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddRecipeDialog(
    onDismiss: () -> Unit,
    onSave: (Recipe, List<RecipeIngredient>) -> Unit,
    modifier: Modifier = Modifier
) {
    var recipeName by remember { mutableStateOf("") }
    var instructions by remember { mutableStateOf("") }
    var servings by remember { mutableStateOf("1") }
    var prepTime by remember { mutableStateOf("0") }
    var cookTime by remember { mutableStateOf("0") }
    var selectedCategory by remember { mutableStateOf(ProductCategory.CAKES) }
    var selectedDifficulty by remember { mutableStateOf(RecipeDifficulty.MEDIUM) }
    var ingredients by remember { mutableStateOf(listOf<RecipeIngredientInput>()) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth(0.9f)
                .fillMaxHeight(0.9f)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp)
            ) {
                // Header
                Text(
                    text = "Add New Recipe",
                    style = MaterialTheme.typography.headlineSmall
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Basic Information
                    item {
                        Text(
                            text = "Basic Information",
                            style = MaterialTheme.typography.titleMedium
                        )
                    }
                    
                    item {
                        OutlinedTextField(
                            value = recipeName,
                            onValueChange = { recipeName = it },
                            label = { Text("Recipe Name") },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true
                        )
                    }
                    
                    item {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            // Category dropdown
                            var categoryExpanded by remember { mutableStateOf(false) }
                            ExposedDropdownMenuBox(
                                expanded = categoryExpanded,
                                onExpandedChange = { categoryExpanded = it },
                                modifier = Modifier.weight(1f)
                            ) {
                                OutlinedTextField(
                                    value = selectedCategory.displayName,
                                    onValueChange = {},
                                    readOnly = true,
                                    label = { Text("Category") },
                                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = categoryExpanded) },
                                    modifier = Modifier.menuAnchor()
                                )
                                ExposedDropdownMenu(
                                    expanded = categoryExpanded,
                                    onDismissRequest = { categoryExpanded = false }
                                ) {
                                    ProductCategory.values().forEach { category ->
                                        DropdownMenuItem(
                                            text = { Text(category.displayName) },
                                            onClick = {
                                                selectedCategory = category
                                                categoryExpanded = false
                                            }
                                        )
                                    }
                                }
                            }
                            
                            // Difficulty dropdown
                            var difficultyExpanded by remember { mutableStateOf(false) }
                            ExposedDropdownMenuBox(
                                expanded = difficultyExpanded,
                                onExpandedChange = { difficultyExpanded = it },
                                modifier = Modifier.weight(1f)
                            ) {
                                OutlinedTextField(
                                    value = selectedDifficulty.displayName,
                                    onValueChange = {},
                                    readOnly = true,
                                    label = { Text("Difficulty") },
                                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = difficultyExpanded) },
                                    modifier = Modifier.menuAnchor()
                                )
                                ExposedDropdownMenu(
                                    expanded = difficultyExpanded,
                                    onDismissRequest = { difficultyExpanded = false }
                                ) {
                                    RecipeDifficulty.values().forEach { difficulty ->
                                        DropdownMenuItem(
                                            text = { Text(difficulty.displayName) },
                                            onClick = {
                                                selectedDifficulty = difficulty
                                                difficultyExpanded = false
                                            }
                                        )
                                    }
                                }
                            }
                        }
                    }
                    
                    item {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            OutlinedTextField(
                                value = servings,
                                onValueChange = { servings = it },
                                label = { Text("Servings") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                modifier = Modifier.weight(1f),
                                singleLine = true
                            )
                            
                            OutlinedTextField(
                                value = prepTime,
                                onValueChange = { prepTime = it },
                                label = { Text("Prep Time (min)") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                modifier = Modifier.weight(1f),
                                singleLine = true
                            )
                            
                            OutlinedTextField(
                                value = cookTime,
                                onValueChange = { cookTime = it },
                                label = { Text("Cook Time (min)") },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                modifier = Modifier.weight(1f),
                                singleLine = true
                            )
                        }
                    }
                    
                    item {
                        OutlinedTextField(
                            value = instructions,
                            onValueChange = { instructions = it },
                            label = { Text("Instructions") },
                            modifier = Modifier.fillMaxWidth(),
                            minLines = 3,
                            maxLines = 5
                        )
                    }
                    
                    // Ingredients section
                    item {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Ingredients",
                                style = MaterialTheme.typography.titleMedium
                            )
                            
                            Button(
                                onClick = {
                                    ingredients = ingredients + RecipeIngredientInput()
                                }
                            ) {
                                Icon(Icons.Default.Add, contentDescription = null)
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("Add Ingredient")
                            }
                        }
                    }
                    
                    items(ingredients.indices.toList()) { index ->
                        IngredientInputRow(
                            ingredient = ingredients[index],
                            onUpdate = { updated ->
                                ingredients = ingredients.toMutableList().apply {
                                    set(index, updated)
                                }
                            },
                            onDelete = {
                                ingredients = ingredients.toMutableList().apply {
                                    removeAt(index)
                                }
                            }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            val recipe = Recipe(
                                productId = 0, // Will be set when linking to product
                                name = recipeName,
                                instructions = instructions,
                                servings = servings.toIntOrNull() ?: 1,
                                prepTime = prepTime.toIntOrNull() ?: 0,
                                cookTime = cookTime.toIntOrNull() ?: 0,
                                difficulty = selectedDifficulty,
                                category = selectedCategory
                            )
                            
                            val recipeIngredients = ingredients.mapIndexed { index, input ->
                                RecipeIngredient(
                                    recipeId = 0, // Will be set when saving
                                    ingredientId = 0, // TODO: Link to actual ingredients
                                    quantity = input.quantity.toDoubleOrNull() ?: 0.0,
                                    unit = input.unit,
                                    notes = input.notes
                                )
                            }
                            
                            onSave(recipe, recipeIngredients)
                        },
                        enabled = recipeName.isNotBlank() && ingredients.isNotEmpty()
                    ) {
                        Text("Save Recipe")
                    }
                }
            }
        }
    }
}

/**
 * Input row for recipe ingredients
 */
@Composable
private fun IngredientInputRow(
    ingredient: RecipeIngredientInput,
    onUpdate: (RecipeIngredientInput) -> Unit,
    onDelete: () -> Unit
) {
    Card {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = ingredient.name,
                onValueChange = { onUpdate(ingredient.copy(name = it)) },
                label = { Text("Ingredient") },
                modifier = Modifier.weight(2f),
                singleLine = true
            )
            
            OutlinedTextField(
                value = ingredient.quantity,
                onValueChange = { onUpdate(ingredient.copy(quantity = it)) },
                label = { Text("Quantity") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                modifier = Modifier.weight(1f),
                singleLine = true
            )
            
            OutlinedTextField(
                value = ingredient.unit,
                onValueChange = { onUpdate(ingredient.copy(unit = it)) },
                label = { Text("Unit") },
                modifier = Modifier.weight(1f),
                singleLine = true
            )
            
            IconButton(onClick = onDelete) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "Remove ingredient",
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

/**
 * Data class for ingredient input
 */
data class RecipeIngredientInput(
    val name: String = "",
    val quantity: String = "",
    val unit: String = "",
    val notes: String = ""
)

@Preview(showBackground = true)
@Composable
fun AddRecipeDialogPreview() {
    BakeryTrackerTheme {
        AddRecipeDialog(
            onDismiss = {},
            onSave = { _, _ -> }
        )
    }
}
