package com.bakery.tracker.domain.usecase

import com.bakery.tracker.data.local.BakeryDatabase
import com.bakery.tracker.data.models.ProductionLog
import com.bakery.tracker.data.repository.IngredientRepository
import com.bakery.tracker.data.repository.ProductRepository
import com.bakery.tracker.data.repository.RecipeRepository
import com.bakery.tracker.util.IngredientCalculator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Use case for recording production and updating ingredient stock
 */
class RecordProductionUseCase @Inject constructor(
    private val database: BakeryDatabase,
    private val ingredientRepository: IngredientRepository,
    private val productRepository: ProductRepository,
    private val recipeRepository: RecipeRepository
) {
    
    /**
     * Execute the use case to record production
     * 
     * @param productionLog The production log to record
     * @param recipeId Optional recipe ID to calculate ingredient usage
     * @return Result containing the production log ID or error
     */
    suspend operator fun invoke(
        productionLog: ProductionLog,
        recipeId: Long? = null
    ): Result<ProductionLogResult> = withContext(Dispatchers.IO) {
        try {
            // Validate production data
            validateProductionLog(productionLog)
            
            val result = database.runInTransaction {
                // Get recipe ingredients if recipe is specified
                val ingredientUsage = if (recipeId != null) {
                    calculateIngredientUsage(recipeId, productionLog.quantityProduced)
                } else {
                    emptyMap()
                }
                
                // Check if there's sufficient stock for all ingredients
                if (ingredientUsage.isNotEmpty()) {
                    val availableStock = getAvailableStock(ingredientUsage.keys.toList())
                    if (!IngredientCalculator.hasSufficientStock(ingredientUsage, availableStock)) {
                        val missingIngredients = IngredientCalculator.getMissingIngredients(ingredientUsage, availableStock)
                        throw InsufficientStockException("Insufficient stock for ingredients: $missingIngredients")
                    }
                }
                
                // Insert production log
                val productionLogId = database.productionDao().insertProductionLog(productionLog)
                
                // Update ingredient stock
                val stockUpdates = mutableListOf<StockUpdate>()
                ingredientUsage.forEach { (ingredientId, usedQuantity) ->
                    val success = ingredientRepository.decreaseStock(ingredientId, usedQuantity)
                    if (success) {
                        stockUpdates.add(StockUpdate(ingredientId, usedQuantity))
                    } else {
                        throw InsufficientStockException("Failed to update stock for ingredient $ingredientId")
                    }
                }
                
                ProductionLogResult(
                    productionLogId = productionLogId,
                    stockUpdates = stockUpdates
                )
            }
            
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Validate production log data
     */
    private fun validateProductionLog(productionLog: ProductionLog) {
        require(productionLog.quantityProduced > 0) { "Production quantity must be greater than 0" }
        require(productionLog.productionDate > 0) { "Production date must be valid" }
    }
    
    /**
     * Calculate ingredient usage for a recipe and quantity
     */
    private suspend fun calculateIngredientUsage(recipeId: Long, quantity: Int): Map<Long, Double> {
        val recipe = recipeRepository.getRecipeById(recipeId)
            ?: throw IllegalArgumentException("Recipe not found: $recipeId")
        
        val recipeIngredients = recipeRepository.getRecipeIngredients(recipeId)
        
        return IngredientCalculator.calculateIngredientRequirements(
            recipeIngredients = recipeIngredients,
            batchSize = recipe.batchSize,
            desiredQuantity = quantity
        )
    }
    
    /**
     * Get available stock for ingredients
     */
    private suspend fun getAvailableStock(ingredientIds: List<Long>): Map<Long, Double> {
        return ingredientIds.associateWith { ingredientId ->
            ingredientRepository.getIngredientById(ingredientId)?.currentStock ?: 0.0
        }
    }
}

/**
 * Result of production recording
 */
data class ProductionLogResult(
    val productionLogId: Long,
    val stockUpdates: List<StockUpdate>
)

/**
 * Stock update information for undo functionality
 */
data class StockUpdate(
    val ingredientId: Long,
    val quantityUsed: Double
)

/**
 * Exception thrown when there's insufficient stock
 */
class InsufficientStockException(message: String) : Exception(message)
