package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * Represents an item in a bill/invoice.
 * 
 * @param id Unique identifier for the bill item
 * @param billId ID of the bill this item belongs to
 * @param productId ID of the product
 * @param productName Name of the product at time of sale
 * @param quantity Quantity of the product
 * @param unitPrice Price per unit at time of sale
 * @param totalPrice Total price for this item (quantity * unitPrice)
 * @param notes Optional notes for this item
 */
@Entity(
    tableName = "bill_items",
    foreignKeys = [
        ForeignKey(
            entity = Bill::class,
            parentColumns = ["id"],
            childColumns = ["billId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = Product::class,
            parentColumns = ["id"],
            childColumns = ["productId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["billId"]),
        Index(value = ["productId"])
    ]
)
data class BillItem(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val billId: Long,
    val productId: Long,
    val productName: String,
    val quantity: Int,
    val unitPrice: Double,
    val totalPrice: Double,
    val notes: String = ""
) {
    /**
     * Calculates the total price (quantity * unitPrice)
     * This is a helper function to ensure consistency
     */
    fun calculateTotalPrice(): Double = quantity * unitPrice
}
