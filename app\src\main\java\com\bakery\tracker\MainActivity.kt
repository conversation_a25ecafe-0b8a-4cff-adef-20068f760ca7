package com.bakery.tracker

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.bakery.tracker.ui.navigation.BakeryTrackerNavigation
import com.bakery.tracker.ui.theme.BakeryTrackerTheme
import com.bakery.tracker.ui.viewmodel.ThemeViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Main activity for the Bakery Tracker application
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            BakeryTrackerApp()
        }
    }
}

/**
 * Main app composable that sets up the theme and navigation
 */
@Composable
fun BakeryTrackerApp(
    themeViewModel: ThemeViewModel = hiltViewModel()
) {
    val isDarkTheme by themeViewModel.isDarkTheme.collectAsState()
    
    BakeryTrackerTheme(
        darkTheme = isDarkTheme
    ) {
        Scaffold(
            modifier = Modifier.fillMaxSize()
        ) { innerPadding ->
            BakeryTrackerNavigation(
                modifier = Modifier.padding(innerPadding)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun BakeryTrackerAppPreview() {
    BakeryTrackerTheme {
        BakeryTrackerApp()
    }
}
