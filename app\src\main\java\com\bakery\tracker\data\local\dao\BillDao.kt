package com.bakery.tracker.data.local.dao

import androidx.room.*
import com.bakery.tracker.data.models.Bill
import com.bakery.tracker.data.models.BillItem
import com.bakery.tracker.data.models.PaymentStatus
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for Bill operations
 */
@Dao
interface BillDao {
    
    /**
     * Get all bills as a Flow for reactive updates
     */
    @Query("SELECT * FROM bills ORDER BY createdAt DESC")
    fun getAllBills(): Flow<List<Bill>>
    
    /**
     * Get bills by payment status
     */
    @Query("SELECT * FROM bills WHERE paymentStatus = :status ORDER BY createdAt DESC")
    fun getBillsByStatus(status: PaymentStatus): Flow<List<Bill>>
    
    /**
     * Get bills for a specific date range
     */
    @Query("SELECT * FROM bills WHERE createdAt BETWEEN :startDate AND :endDate ORDER BY createdAt DESC")
    fun getBillsByDateRange(startDate: Long, endDate: Long): Flow<List<Bill>>
    
    /**
     * Get bills for today
     */
    @Query("SELECT * FROM bills WHERE createdAt >= :startOfDay AND createdAt < :endOfDay ORDER BY createdAt DESC")
    fun getTodayBills(startOfDay: Long, endOfDay: Long): Flow<List<Bill>>
    
    /**
     * Get bills for current month
     */
    @Query("SELECT * FROM bills WHERE createdAt >= :startOfMonth ORDER BY createdAt DESC")
    fun getCurrentMonthBills(startOfMonth: Long): Flow<List<Bill>>
    
    /**
     * Get bill by ID
     */
    @Query("SELECT * FROM bills WHERE id = :id")
    suspend fun getBillById(id: Long): Bill?
    
    /**
     * Get bill by bill number
     */
    @Query("SELECT * FROM bills WHERE billNumber = :billNumber LIMIT 1")
    suspend fun getBillByNumber(billNumber: String): Bill?
    
    /**
     * Search bills by customer name
     */
    @Query("SELECT * FROM bills WHERE customerName LIKE '%' || :searchQuery || '%' ORDER BY createdAt DESC")
    fun searchBillsByCustomer(searchQuery: String): Flow<List<Bill>>
    
    /**
     * Insert a new bill
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBill(bill: Bill): Long
    
    /**
     * Update an existing bill
     */
    @Update
    suspend fun updateBill(bill: Bill)
    
    /**
     * Update bill payment status
     */
    @Query("UPDATE bills SET paymentStatus = :status, paidAt = :paidAt WHERE id = :id")
    suspend fun updateBillPaymentStatus(id: Long, status: PaymentStatus, paidAt: Long? = null)
    
    /**
     * Delete a bill
     */
    @Delete
    suspend fun deleteBill(bill: Bill)
    
    /**
     * Delete bill by ID
     */
    @Query("DELETE FROM bills WHERE id = :id")
    suspend fun deleteBillById(id: Long)
    
    // Bill Items operations
    
    /**
     * Get all items for a bill
     */
    @Query("SELECT * FROM bill_items WHERE billId = :billId ORDER BY id ASC")
    suspend fun getBillItems(billId: Long): List<BillItem>
    
    /**
     * Get bill items with product details
     */
    @Query("""
        SELECT bi.*, p.name as productName, p.category
        FROM bill_items bi
        INNER JOIN products p ON bi.productId = p.id
        WHERE bi.billId = :billId
        ORDER BY bi.id ASC
    """)
    suspend fun getBillItemsWithProductDetails(billId: Long): List<BillItemWithProductDetails>
    
    /**
     * Insert bill item
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBillItem(billItem: BillItem): Long
    
    /**
     * Insert multiple bill items
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBillItems(billItems: List<BillItem>): List<Long>
    
    /**
     * Update bill item
     */
    @Update
    suspend fun updateBillItem(billItem: BillItem)
    
    /**
     * Delete bill item
     */
    @Delete
    suspend fun deleteBillItem(billItem: BillItem)
    
    /**
     * Delete all items for a bill
     */
    @Query("DELETE FROM bill_items WHERE billId = :billId")
    suspend fun deleteAllBillItems(billId: Long)
    
    /**
     * Get total revenue for date range
     */
    @Query("SELECT SUM(totalAmount) FROM bills WHERE paymentStatus = 'PAID' AND createdAt BETWEEN :startDate AND :endDate")
    suspend fun getTotalRevenue(startDate: Long, endDate: Long): Double?
    
    /**
     * Get daily revenue summary
     */
    @Query("""
        SELECT 
            DATE(createdAt/1000, 'unixepoch') as date,
            SUM(totalAmount) as totalRevenue,
            COUNT(*) as billCount
        FROM bills 
        WHERE paymentStatus = 'PAID' AND createdAt BETWEEN :startDate AND :endDate
        GROUP BY DATE(createdAt/1000, 'unixepoch')
        ORDER BY date DESC
    """)
    suspend fun getDailyRevenueSummary(startDate: Long, endDate: Long): List<DailyRevenueSummary>
    
    /**
     * Get top selling products
     */
    @Query("""
        SELECT 
            bi.productId,
            bi.productName,
            SUM(bi.quantity) as totalQuantitySold,
            SUM(bi.totalPrice) as totalRevenue
        FROM bill_items bi
        INNER JOIN bills b ON bi.billId = b.id
        WHERE b.paymentStatus = 'PAID' AND b.createdAt BETWEEN :startDate AND :endDate
        GROUP BY bi.productId, bi.productName
        ORDER BY totalQuantitySold DESC
        LIMIT :limit
    """)
    suspend fun getTopSellingProducts(startDate: Long, endDate: Long, limit: Int = 10): List<TopSellingProduct>
    
    /**
     * Get total count of bills
     */
    @Query("SELECT COUNT(*) FROM bills")
    suspend fun getBillCount(): Int
    
    /**
     * Get count of paid bills
     */
    @Query("SELECT COUNT(*) FROM bills WHERE paymentStatus = 'PAID'")
    suspend fun getPaidBillCount(): Int
}

/**
 * Data class for bill item with product details
 */
data class BillItemWithProductDetails(
    val id: Long,
    val billId: Long,
    val productId: Long,
    val productName: String,
    val quantity: Int,
    val unitPrice: Double,
    val totalPrice: Double,
    val notes: String,
    val category: String
)

/**
 * Data class for daily revenue summary
 */
data class DailyRevenueSummary(
    val date: String,
    val totalRevenue: Double,
    val billCount: Int
)

/**
 * Data class for top selling products
 */
data class TopSellingProduct(
    val productId: Long,
    val productName: String,
    val totalQuantitySold: Int,
    val totalRevenue: Double
)
