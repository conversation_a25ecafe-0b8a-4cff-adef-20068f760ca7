# 📱 **Wireless Debugging Setup Guide**

## 🎯 **Setup Wireless Debugging for Bakery Tracker Pro**

### **Prerequisites:**
- ✅ Android device (Android 11+ recommended for best wireless debugging)
- ✅ Device and computer on same WiFi network
- ✅ USB cable (for initial setup only)
- ✅ Android Studio with ADB

---

## 🔧 **Method 1: Android 11+ (Easiest)**

### **Step 1: Enable Developer Options**
1. **Settings → About Phone**
2. **Tap "Build Number" 7 times**
3. **Enter your PIN/password**
4. **"Developer options" now appears in Settings**

### **Step 2: Enable Wireless Debugging**
1. **Settings → Developer Options**
2. **Enable "USB Debugging"** ✅
3. **Enable "Wireless Debugging"** ✅
4. **Tap "Wireless Debugging"**
5. **Tap "Pair device with pairing code"**
6. **Note the IP address and port** (e.g., *************:37045)
7. **Note the pairing code** (e.g., 123456)

### **Step 3: Connect from Android Studio**
1. **Open Android Studio**
2. **View → Tool Windows → Device Manager**
3. **Click "Pair Devices Using Wi-Fi"**
4. **Enter the IP:Port and pairing code from your device**
5. **Click "Pair"**
6. **Device should appear as "Connected"**

---

## 🔧 **Method 2: Any Android Version (ADB Commands)**

### **Step 1: Initial USB Connection**
1. **Connect device via USB**
2. **Enable USB Debugging** (Settings → Developer Options)
3. **Accept "Allow USB Debugging" prompt**

### **Step 2: Setup Wireless Connection**
```bash
# Open Command Prompt/PowerShell in Android Studio terminal
# Or navigate to Android SDK platform-tools directory

# Check device is connected
adb devices

# Enable TCP/IP mode on port 5555
adb tcpip 5555

# Find your device's IP address
# Method A: Check device WiFi settings
# Method B: Use ADB
adb shell ip route | grep wlan

# Disconnect USB cable now
# Connect wirelessly (replace with your device's IP)
adb connect *************:5555

# Verify wireless connection
adb devices
```

### **Step 3: Test Connection**
```bash
# Should show your device wirelessly connected
adb devices

# Example output:
# *************:5555    device
```

---

## 🚀 **Deploy Bakery Tracker Pro APK**

### **Option A: Direct Install via ADB**
```bash
# After building APK in Android Studio
# Navigate to project directory
cd path\to\bakerytrack

# Install APK wirelessly
adb install app\build\outputs\apk\debug\app-debug.apk

# Or force reinstall if already installed
adb install -r app\build\outputs\apk\debug\app-debug.apk
```

### **Option B: Run from Android Studio**
1. **Build → Generate APK** (if not done already)
2. **Select your wireless device** in device dropdown
3. **Run → Run 'app'**
4. **App will install and launch automatically**

---

## 📱 **Testing Bakery Tracker Pro Features**

### **Login Testing:**
1. **Launch app on device**
2. **Test Admin login**: `<EMAIL>` / `admin123`
3. **Test Staff login**: `<EMAIL>` / `staff123`

### **POS System Testing:**
1. **Navigate to POS tab**
2. **Browse product categories**: Cakes, Pastries, Breads, Beverages
3. **Add items to cart**: Chocolate Cake (₹450), Croissants (₹80)
4. **Test quantity controls**: +/- buttons
5. **Check cart total calculation**
6. **Test search functionality**

### **Dashboard Testing:**
1. **View analytics**: Today's sales ₹12,450
2. **Check quick stats**: 47 orders, 156 production items
3. **Scroll through recent activities**
4. **Test dark/light theme toggle**

### **Recipe Management Testing:**
1. **Navigate to Recipes tab**
2. **Browse existing recipes**
3. **Test "Add Recipe" button**
4. **Fill recipe form with ingredients**
5. **Test category filtering**
6. **Test recipe search**

### **Role-Based Navigation Testing:**
1. **Login as Staff**: Should see Dashboard, POS, Recipes, Production, Stock
2. **Login as Admin**: Should see all Staff features + Orders, Pricing, Staff, Settings
3. **Test navigation between screens**

---

## 🔍 **Troubleshooting Wireless Debugging**

### **Connection Issues:**
```bash
# If connection drops
adb disconnect
adb connect *************:5555

# Reset ADB if needed
adb kill-server
adb start-server

# Check connection status
adb devices -l
```

### **Common Problems:**

#### **"Device not found"**
- ✅ Ensure both devices on same WiFi
- ✅ Check device IP address hasn't changed
- ✅ Restart wireless debugging on device

#### **"Connection refused"**
- ✅ Re-enable wireless debugging
- ✅ Check firewall settings
- ✅ Try different port: `adb tcpip 5037`

#### **"Unauthorized device"**
- ✅ Accept debugging prompt on device
- ✅ Check "Always allow from this computer"

---

## 📊 **Performance Testing on Device**

### **Test These Scenarios:**
1. **App startup time**: Should launch quickly
2. **Navigation smoothness**: Transitions between screens
3. **Cart performance**: Adding/removing multiple items
4. **Database operations**: Recipe CRUD operations
5. **Memory usage**: Monitor in Android Studio profiler
6. **Battery impact**: Check power consumption

### **Expected Performance:**
- ✅ **Smooth 60fps** navigation
- ✅ **Fast database queries** (Room optimization)
- ✅ **Responsive UI** (Compose performance)
- ✅ **Low memory usage** (~50-100MB)
- ✅ **Quick startup** (<3 seconds)

---

## 🎉 **Success Indicators**

### **Wireless Connection:**
- ✅ Device appears in Android Studio device list
- ✅ `adb devices` shows device with IP:port
- ✅ Can install/run apps wirelessly

### **App Functionality:**
- ✅ Login works with demo credentials
- ✅ POS cart calculations are correct
- ✅ Dashboard shows analytics data
- ✅ Recipe management CRUD works
- ✅ Role-based navigation functions
- ✅ Dark/light theme toggle works
- ✅ No crashes or ANRs

---

## 🚀 **Quick Setup Summary**

**For Android 11+:**
1. Enable Wireless Debugging in Developer Options
2. Pair with Android Studio using pairing code
3. Run app directly from Android Studio

**For Older Android:**
1. Connect USB → `adb tcpip 5555` → Disconnect USB
2. `adb connect DEVICE_IP:5555`
3. Install APK: `adb install app-debug.apk`

**Then test all Bakery Tracker Pro features wirelessly!** 📱✨

---

**Your complete bakery management app is ready for wireless testing!** 🥖🚀
