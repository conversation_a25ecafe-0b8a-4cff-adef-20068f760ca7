package com.bakery.tracker.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.bakery.tracker.data.models.*
import com.bakery.tracker.ui.components.ProductCard
import com.bakery.tracker.ui.components.CartItemCard
import com.bakery.tracker.ui.components.CategoryChip
import com.bakery.tracker.ui.theme.BakeryTrackerTheme
import com.bakery.tracker.ui.viewmodel.POSViewModel

/**
 * Point of Sale (POS) Screen - Main selling interface
 * Implements the POS system as described in overview.txt
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun POSScreen(
    posViewModel: POSViewModel = hiltViewModel()
) {
    val uiState by posViewModel.uiState.collectAsState()
    
    Row(
        modifier = Modifier.fillMaxSize()
    ) {
        // Left side - Product selection (70% width)
        Column(
            modifier = Modifier
                .weight(0.7f)
                .fillMaxHeight()
                .padding(16.dp)
        ) {
            // Header with search
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Products",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
                
                OutlinedTextField(
                    value = uiState.searchQuery,
                    onValueChange = posViewModel::updateSearchQuery,
                    placeholder = { Text("Search products...") },
                    leadingIcon = { Icon(Icons.Default.Search, contentDescription = "Search") },
                    modifier = Modifier.width(300.dp),
                    singleLine = true
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Category filters
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(horizontal = 4.dp)
            ) {
                item {
                    CategoryChip(
                        category = "All",
                        isSelected = uiState.selectedCategory == null,
                        onClick = { posViewModel.selectCategory(null) }
                    )
                }
                items(ProductCategory.values()) { category ->
                    CategoryChip(
                        category = category.displayName,
                        isSelected = uiState.selectedCategory == category,
                        onClick = { posViewModel.selectCategory(category) }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Popular items section
            if (uiState.popularProducts.isNotEmpty()) {
                Text(
                    text = "Popular Items",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(horizontal = 4.dp)
                ) {
                    items(uiState.popularProducts) { product ->
                        ProductCard(
                            product = product,
                            onAddToCart = { posViewModel.addToCart(product) },
                            modifier = Modifier.width(200.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
            }
            
            // All products grid
            Text(
                text = "All Products",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(uiState.filteredProducts.chunked(3)) { productRow ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        productRow.forEach { product ->
                            ProductCard(
                                product = product,
                                onAddToCart = { posViewModel.addToCart(product) },
                                modifier = Modifier.weight(1f)
                            )
                        }
                        // Fill remaining space if row is not complete
                        repeat(3 - productRow.size) {
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }
                }
            }
        }
        
        // Right side - Shopping cart (30% width)
        Card(
            modifier = Modifier
                .weight(0.3f)
                .fillMaxHeight(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Cart header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Cart",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                    
                    if (uiState.cartItems.isNotEmpty()) {
                        TextButton(
                            onClick = posViewModel::clearCart
                        ) {
                            Text("Clear All")
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                if (uiState.cartItems.isEmpty()) {
                    // Empty cart state
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.ShoppingCart,
                            contentDescription = "Empty cart",
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "Cart is empty",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "Add products to get started",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                } else {
                    // Cart items
                    LazyColumn(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(uiState.cartItems) { cartItem ->
                            CartItemCard(
                                cartItem = cartItem,
                                onQuantityChange = { newQuantity ->
                                    posViewModel.updateCartItemQuantity(cartItem.id, newQuantity)
                                },
                                onRemove = {
                                    posViewModel.removeFromCart(cartItem.id)
                                }
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Cart summary
                    Card {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text("Items:")
                                Text("${uiState.cartSummary.itemCount}")
                            }
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "Total:",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold
                                )
                                Text(
                                    text = "₹${String.format("%.2f", uiState.cartSummary.subtotal)}",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Checkout button
                    Button(
                        onClick = posViewModel::proceedToCheckout,
                        modifier = Modifier.fillMaxWidth(),
                        enabled = uiState.cartItems.isNotEmpty()
                    ) {
                        Icon(Icons.Default.Payment, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Proceed to Checkout")
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun POSScreenPreview() {
    BakeryTrackerTheme {
        POSScreen()
    }
}
