package com.bakery.tracker.data.local.dao

import androidx.room.*
import com.bakery.tracker.data.models.ProductionLog
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for ProductionLog operations
 */
@Dao
interface ProductionDao {
    
    /**
     * Get all production logs as a Flow for reactive updates
     */
    @Query("SELECT * FROM production_logs ORDER BY productionDate DESC")
    fun getAllProductionLogs(): Flow<List<ProductionLog>>
    
    /**
     * Get production logs for a specific product
     */
    @Query("SELECT * FROM production_logs WHERE productId = :productId ORDER BY productionDate DESC")
    fun getProductionLogsForProduct(productId: Long): Flow<List<ProductionLog>>
    
    /**
     * Get production logs for a specific date range
     */
    @Query("SELECT * FROM production_logs WHERE productionDate BETWEEN :startDate AND :endDate ORDER BY productionDate DESC")
    fun getProductionLogsByDateRange(startDate: Long, endDate: Long): Flow<List<ProductionLog>>
    
    /**
     * Get production logs for today
     */
    @Query("SELECT * FROM production_logs WHERE productionDate >= :startOfDay AND productionDate < :endOfDay ORDER BY productionDate DESC")
    fun getTodayProductionLogs(startOfDay: Long, endOfDay: Long): Flow<List<ProductionLog>>
    
    /**
     * Get production logs for current month
     */
    @Query("SELECT * FROM production_logs WHERE productionDate >= :startOfMonth ORDER BY productionDate DESC")
    fun getCurrentMonthProductionLogs(startOfMonth: Long): Flow<List<ProductionLog>>
    
    /**
     * Get production log by ID
     */
    @Query("SELECT * FROM production_logs WHERE id = :id")
    suspend fun getProductionLogById(id: Long): ProductionLog?
    
    /**
     * Get production logs by batch number
     */
    @Query("SELECT * FROM production_logs WHERE batchNumber = :batchNumber ORDER BY productionDate DESC")
    suspend fun getProductionLogsByBatchNumber(batchNumber: String): List<ProductionLog>
    
    /**
     * Get production logs by staff member
     */
    @Query("SELECT * FROM production_logs WHERE staffMember = :staffMember ORDER BY productionDate DESC")
    fun getProductionLogsByStaffMember(staffMember: String): Flow<List<ProductionLog>>
    
    /**
     * Insert a new production log
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProductionLog(productionLog: ProductionLog): Long
    
    /**
     * Insert multiple production logs
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProductionLogs(productionLogs: List<ProductionLog>): List<Long>
    
    /**
     * Update an existing production log
     */
    @Update
    suspend fun updateProductionLog(productionLog: ProductionLog)
    
    /**
     * Delete a production log
     */
    @Delete
    suspend fun deleteProductionLog(productionLog: ProductionLog)
    
    /**
     * Delete production log by ID
     */
    @Query("DELETE FROM production_logs WHERE id = :id")
    suspend fun deleteProductionLogById(id: Long)
    
    /**
     * Get total production quantity for a product in date range
     */
    @Query("SELECT SUM(quantityProduced) FROM production_logs WHERE productId = :productId AND productionDate BETWEEN :startDate AND :endDate")
    suspend fun getTotalProductionForProduct(productId: Long, startDate: Long, endDate: Long): Int?
    
    /**
     * Get total production cost for date range
     */
    @Query("SELECT SUM(totalCost) FROM production_logs WHERE productionDate BETWEEN :startDate AND :endDate")
    suspend fun getTotalProductionCost(startDate: Long, endDate: Long): Double?
    
    /**
     * Get daily production summary
     */
    @Query("""
        SELECT 
            DATE(productionDate/1000, 'unixepoch') as date,
            SUM(quantityProduced) as totalQuantity,
            SUM(totalCost) as totalCost,
            COUNT(*) as batchCount
        FROM production_logs 
        WHERE productionDate BETWEEN :startDate AND :endDate
        GROUP BY DATE(productionDate/1000, 'unixepoch')
        ORDER BY date DESC
    """)
    suspend fun getDailyProductionSummary(startDate: Long, endDate: Long): List<DailyProductionSummary>
    
    /**
     * Get production summary by product
     */
    @Query("""
        SELECT 
            productId,
            SUM(quantityProduced) as totalQuantity,
            SUM(totalCost) as totalCost,
            COUNT(*) as batchCount,
            AVG(costPerUnit) as avgCostPerUnit
        FROM production_logs 
        WHERE productionDate BETWEEN :startDate AND :endDate
        GROUP BY productId
        ORDER BY totalQuantity DESC
    """)
    suspend fun getProductionSummaryByProduct(startDate: Long, endDate: Long): List<ProductProductionSummary>
    
    /**
     * Get total count of production logs
     */
    @Query("SELECT COUNT(*) FROM production_logs")
    suspend fun getProductionLogCount(): Int
}

/**
 * Data class for daily production summary
 */
data class DailyProductionSummary(
    val date: String,
    val totalQuantity: Int,
    val totalCost: Double,
    val batchCount: Int
)

/**
 * Data class for product production summary
 */
data class ProductProductionSummary(
    val productId: Long,
    val totalQuantity: Int,
    val totalCost: Double,
    val batchCount: Int,
    val avgCostPerUnit: Double
)
