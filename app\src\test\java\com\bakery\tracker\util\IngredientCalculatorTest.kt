package com.bakery.tracker.util

import com.bakery.tracker.data.models.RecipeIngredient
import org.junit.Assert.*
import org.junit.Test

/**
 * Unit tests for IngredientCalculator
 */
class IngredientCalculatorTest {
    
    @Test
    fun `calculateIngredientRequirements should scale ingredients correctly`() {
        // Given
        val recipeIngredients = listOf(
            RecipeIngredient(1, 1, 1, 2.0, "kg", ""),
            RecipeIngredient(2, 1, 2, 1.5, "kg", "")
        )
        val batchSize = 10
        val desiredQuantity = 20
        
        // When
        val result = IngredientCalculator.calculateIngredientRequirements(
            recipeIngredients, batchSize, desiredQuantity
        )
        
        // Then
        assertEquals(4.0, result[1L], 0.01)
        assertEquals(3.0, result[2L], 0.01)
    }
    
    @Test
    fun `calculateRecipeCost should calculate total cost correctly`() {
        // Given
        val recipeIngredients = listOf(
            RecipeIngredientWithPrice(1, 2.0, "kg", 5.0, "Flour"),
            RecipeIngredientWithPrice(2, 1.0, "kg", 3.0, "Sugar")
        )
        val batchSize = 10
        val desiredQuantity = 20
        
        // When
        val result = IngredientCalculator.calculateRecipeCost(
            recipeIngredients, batchSize, desiredQuantity
        )
        
        // Then
        // (2.0 * 5.0 + 1.0 * 3.0) * 2 = 26.0
        assertEquals(26.0, result, 0.01)
    }
    
    @Test
    fun `calculateCostPerUnit should calculate correctly`() {
        // Given
        val totalCost = 100.0
        val quantity = 20
        
        // When
        val result = IngredientCalculator.calculateCostPerUnit(totalCost, quantity)
        
        // Then
        assertEquals(5.0, result, 0.01)
    }
    
    @Test
    fun `calculateCostPerUnit should return zero for zero quantity`() {
        // Given
        val totalCost = 100.0
        val quantity = 0
        
        // When
        val result = IngredientCalculator.calculateCostPerUnit(totalCost, quantity)
        
        // Then
        assertEquals(0.0, result, 0.01)
    }
    
    @Test
    fun `hasSufficientStock should return true when stock is sufficient`() {
        // Given
        val requirements = mapOf(1L to 5.0, 2L to 3.0)
        val availableStock = mapOf(1L to 10.0, 2L to 5.0)
        
        // When
        val result = IngredientCalculator.hasSufficientStock(requirements, availableStock)
        
        // Then
        assertTrue(result)
    }
    
    @Test
    fun `hasSufficientStock should return false when stock is insufficient`() {
        // Given
        val requirements = mapOf(1L to 5.0, 2L to 3.0)
        val availableStock = mapOf(1L to 3.0, 2L to 5.0)
        
        // When
        val result = IngredientCalculator.hasSufficientStock(requirements, availableStock)
        
        // Then
        assertFalse(result)
    }
    
    @Test
    fun `getMissingIngredients should return correct shortages`() {
        // Given
        val requirements = mapOf(1L to 5.0, 2L to 3.0)
        val availableStock = mapOf(1L to 3.0, 2L to 5.0)
        
        // When
        val result = IngredientCalculator.getMissingIngredients(requirements, availableStock)
        
        // Then
        assertEquals(1, result.size)
        assertEquals(2.0, result[1L], 0.01)
    }
    
    @Test
    fun `calculateMaxPossibleBatches should return correct number`() {
        // Given
        val recipeIngredients = listOf(
            RecipeIngredient(1, 1, 1, 2.0, "kg", ""),
            RecipeIngredient(2, 1, 2, 1.0, "kg", "")
        )
        val availableStock = mapOf(1L to 10.0, 2L to 5.0)
        
        // When
        val result = IngredientCalculator.calculateMaxPossibleBatches(recipeIngredients, availableStock)
        
        // Then
        assertEquals(5, result) // Limited by ingredient 2: 5.0 / 1.0 = 5
    }
    
    @Test
    fun `convertUnits should convert kg to g correctly`() {
        // Given
        val quantity = 2.5
        val fromUnit = "kg"
        val toUnit = "g"
        
        // When
        val result = IngredientCalculator.convertUnits(quantity, fromUnit, toUnit)
        
        // Then
        assertEquals(2500.0, result, 0.01)
    }
    
    @Test
    fun `convertUnits should return same value for same units`() {
        // Given
        val quantity = 2.5
        val fromUnit = "kg"
        val toUnit = "kg"
        
        // When
        val result = IngredientCalculator.convertUnits(quantity, fromUnit, toUnit)
        
        // Then
        assertEquals(2.5, result, 0.01)
    }
}
