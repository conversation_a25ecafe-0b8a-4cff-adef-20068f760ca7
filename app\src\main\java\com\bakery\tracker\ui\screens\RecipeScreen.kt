package com.bakery.tracker.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.bakery.tracker.data.models.*
import com.bakery.tracker.ui.components.RecipeCard
import com.bakery.tracker.ui.components.AddRecipeDialog
import com.bakery.tracker.ui.theme.BakeryTrackerTheme
import com.bakery.tracker.ui.viewmodel.RecipeViewModel

/**
 * Recipe management screen - implements recipe features from overview.txt
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RecipeScreen(
    recipeViewModel: RecipeViewModel = hiltViewModel()
) {
    val uiState by recipeViewModel.uiState.collectAsState()
    var showAddRecipeDialog by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header with search
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Recipes",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Manage your bakery recipes",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            OutlinedTextField(
                value = uiState.searchQuery,
                onValueChange = recipeViewModel::updateSearchQuery,
                placeholder = { Text("Search recipes...") },
                leadingIcon = { Icon(Icons.Default.Search, contentDescription = "Search") },
                modifier = Modifier.width(300.dp),
                singleLine = true
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Category filters
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            item {
                FilterChip(
                    onClick = { recipeViewModel.selectCategory(null) },
                    label = { Text("All") },
                    selected = uiState.selectedCategory == null
                )
            }
            items(ProductCategory.values()) { category ->
                FilterChip(
                    onClick = { recipeViewModel.selectCategory(category) },
                    label = { Text(category.displayName) },
                    selected = uiState.selectedCategory == category
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Add recipe button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            Button(
                onClick = { showAddRecipeDialog = true }
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Add Recipe")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Recipes list
        if (uiState.filteredRecipes.isEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "🍞",
                        style = MaterialTheme.typography.displayMedium
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "No recipes found",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Create your first recipe to get started",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(uiState.filteredRecipes) { recipe ->
                    RecipeCard(
                        recipe = recipe,
                        onEdit = { recipeViewModel.editRecipe(recipe) },
                        onDelete = { recipeViewModel.deleteRecipe(recipe) },
                        onToggleActive = { recipeViewModel.toggleRecipeActive(recipe) }
                    )
                }
            }
        }
    }

    // Add Recipe Dialog
    if (showAddRecipeDialog) {
        AddRecipeDialog(
            onDismiss = { showAddRecipeDialog = false },
            onSave = { recipe, ingredients ->
                recipeViewModel.addRecipe(recipe, ingredients)
                showAddRecipeDialog = false
            }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun RecipeScreenPreview() {
    BakeryTrackerTheme {
        RecipeScreen()
    }
}
