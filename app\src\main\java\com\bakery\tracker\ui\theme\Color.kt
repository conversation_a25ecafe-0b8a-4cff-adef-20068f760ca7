package com.bakery.tracker.ui.theme

import androidx.compose.ui.graphics.Color

// Light theme colors
val BakeryPrimary = Color(0xFF8B4513) // Saddle Brown - represents baked goods
val BakeryPrimaryVariant = Color(0xFF5D2F0A) // Darker brown
val BakerySecondary = Color(0xFFFFB74D) // Light Orange - represents golden baked items
val BakerySecondaryVariant = Color(0xFFFF8F00) // Darker orange

// Background colors
val BakeryBackground = Color(0xFFFFFBF0) // Cream white
val BakerySurface = Color(0xFFFFFFFF) // Pure white
val BakeryError = Color(0xFFD32F2F) // Red for errors

// Text colors
val BakeryOnPrimary = Color(0xFFFFFFFF) // White text on primary
val BakeryOnSecondary = Color(0xFF000000) // Black text on secondary
val BakeryOnBackground = Color(0xFF1C1B1F) // Dark text on background
val BakeryOnSurface = Color(0xFF1C1B1F) // Dark text on surface
val BakeryOnError = Color(0xFFFFFFFF) // White text on error

// Dark theme colors
val BakeryPrimaryDark = Color(0xFFD2B48C) // Tan - lighter brown for dark theme
val BakeryPrimaryVariantDark = Color(0xFFA0522D) // Sienna
val BakerySecondaryDark = Color(0xFFFFCC80) // Light orange for dark theme
val BakerySecondaryVariantDark = Color(0xFFFFB74D) // Medium orange

// Dark background colors
val BakeryBackgroundDark = Color(0xFF121212) // Dark background
val BakerySurfaceDark = Color(0xFF1E1E1E) // Dark surface
val BakeryErrorDark = Color(0xFFCF6679) // Light red for dark theme

// Dark text colors
val BakeryOnPrimaryDark = Color(0xFF000000) // Black text on primary (dark theme)
val BakeryOnSecondaryDark = Color(0xFF000000) // Black text on secondary (dark theme)
val BakeryOnBackgroundDark = Color(0xFFE1E2E1) // Light text on dark background
val BakeryOnSurfaceDark = Color(0xFFE1E2E1) // Light text on dark surface
val BakeryOnErrorDark = Color(0xFF000000) // Black text on error (dark theme)

// Additional colors for specific use cases
val LowStockWarning = Color(0xFFFF9800) // Orange for low stock warnings
val LowStockWarningDark = Color(0xFFFFB74D) // Lighter orange for dark theme
val ProfitGreen = Color(0xFF4CAF50) // Green for profit indicators
val ProfitGreenDark = Color(0xFF81C784) // Lighter green for dark theme
val LossRed = Color(0xFFF44336) // Red for loss indicators
val LossRedDark = Color(0xFFE57373) // Lighter red for dark theme

// Chart colors
val ChartColors = listOf(
    Color(0xFF8B4513), // Brown
    Color(0xFFFFB74D), // Orange
    Color(0xFF4CAF50), // Green
    Color(0xFF2196F3), // Blue
    Color(0xFF9C27B0), // Purple
    Color(0xFFFF5722), // Deep Orange
    Color(0xFF607D8B), // Blue Grey
    Color(0xFFE91E63)  // Pink
)

val ChartColorsDark = listOf(
    Color(0xFFD2B48C), // Tan
    Color(0xFFFFCC80), // Light Orange
    Color(0xFF81C784), // Light Green
    Color(0xFF64B5F6), // Light Blue
    Color(0xFFBA68C8), // Light Purple
    Color(0xFFFF8A65), // Light Deep Orange
    Color(0xFF90A4AE), // Light Blue Grey
    Color(0xFFF06292)  // Light Pink
)
