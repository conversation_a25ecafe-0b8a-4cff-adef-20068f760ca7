package com.bakery.tracker.ui.viewmodel

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bakery.tracker.data.models.Ingredient
import com.bakery.tracker.data.repository.IngredientRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Dashboard screen
 */
@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val ingredientRepository: IngredientRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()
    
    init {
        loadDashboardData()
    }
    
    /**
     * Load dashboard data
     */
    private fun loadDashboardData() {
        viewModelScope.launch {
            // Load low stock ingredients
            ingredientRepository.getLowStockIngredients().collect { ingredients ->
                _uiState.value = _uiState.value.copy(
                    lowStockIngredients = ingredients
                )
            }
        }
        
        // Load mock data for demo
        loadMockData()
    }
    
    /**
     * Refresh dashboard data
     */
    fun refreshData() {
        loadDashboardData()
    }
    
    /**
     * Navigate to stock screen for specific ingredient
     */
    fun navigateToStock(ingredientId: Long) {
        // This would typically use navigation
        // For now, it's a placeholder
    }
    
    /**
     * Load mock data for demonstration - implements overview.txt dashboard features
     */
    private fun loadMockData() {
        val mockQuickStats = listOf(
            QuickStat(
                title = "Today's Sales",
                value = "₹12,450",
                subtitle = "revenue",
                icon = Icons.Default.AttachMoney
            ),
            QuickStat(
                title = "Orders",
                value = "47",
                subtitle = "completed",
                icon = Icons.Default.ShoppingCart
            ),
            QuickStat(
                title = "Production",
                value = "156",
                subtitle = "items made",
                icon = Icons.Default.Factory
            ),
            QuickStat(
                title = "Low Stock",
                value = "8",
                subtitle = "ingredients",
                icon = Icons.Default.Warning
            )
        )
        
        val mockTodayProduction = listOf(
            ProductionData("Chocolate Cake", 12),
            ProductionData("Vanilla Cupcakes", 24),
            ProductionData("Croissants", 36),
            ProductionData("Sourdough Bread", 8),
            ProductionData("Blueberry Muffins", 18)
        )

        val mockWeeklyRevenue = listOf(
            RevenueData("Mon", 8500.0),
            RevenueData("Tue", 9200.0),
            RevenueData("Wed", 11000.0),
            RevenueData("Thu", 9800.0),
            RevenueData("Fri", 12500.0),
            RevenueData("Sat", 14000.0),
            RevenueData("Sun", 11500.0)
        )
        
        val mockRecentActivities = listOf(
            RecentActivity(
                title = "Order BT-20241201-1234 completed - ₹850",
                time = "5 minutes ago",
                icon = Icons.Default.CheckCircle
            ),
            RecentActivity(
                title = "12 Chocolate Cakes produced",
                time = "15 minutes ago",
                icon = Icons.Default.Factory
            ),
            RecentActivity(
                title = "Low stock alert: Vanilla Extract",
                time = "30 minutes ago",
                icon = Icons.Default.Warning
            ),
            RecentActivity(
                title = "New customer added: John Doe",
                time = "1 hour ago",
                icon = Icons.Default.Person
            ),
            RecentActivity(
                title = "Recipe updated: Blueberry Muffins",
                time = "2 hours ago",
                icon = Icons.Default.MenuBook
            )
        )
        
        _uiState.value = _uiState.value.copy(
            quickStats = mockQuickStats,
            todayProduction = mockTodayProduction,
            weeklyRevenue = mockWeeklyRevenue,
            recentActivities = mockRecentActivities
        )
    }
}

/**
 * UI state for the Dashboard screen
 */
data class DashboardUiState(
    val isLoading: Boolean = false,
    val quickStats: List<QuickStat> = emptyList(),
    val lowStockIngredients: List<Ingredient> = emptyList(),
    val todayProduction: List<ProductionData> = emptyList(),
    val weeklyRevenue: List<RevenueData> = emptyList(),
    val recentActivities: List<RecentActivity> = emptyList(),
    val error: String? = null
)

/**
 * Data class for quick stats
 */
data class QuickStat(
    val title: String,
    val value: String,
    val subtitle: String,
    val icon: ImageVector
)

/**
 * Data class for production data
 */
data class ProductionData(
    val product: String,
    val quantity: Int
)

/**
 * Data class for revenue data
 */
data class RevenueData(
    val day: String,
    val amount: Double
)

/**
 * Data class for recent activities
 */
data class RecentActivity(
    val title: String,
    val time: String,
    val icon: ImageVector
)
