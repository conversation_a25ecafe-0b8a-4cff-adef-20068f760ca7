package com.bakery.tracker.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.bakery.tracker.ui.theme.BakeryTrackerTheme
import com.bakery.tracker.ui.viewmodel.RevenueData

/**
 * Simple line chart component for revenue data
 */
@Composable
fun RevenueChart(
    data: List<RevenueData>,
    modifier: Modifier = Modifier
) {
    if (data.isEmpty()) {
        Box(
            modifier = modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "No revenue data available",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        return
    }
    
    val maxValue = data.maxOfOrNull { it.amount } ?: 1.0
    val minValue = data.minOfOrNull { it.amount } ?: 0.0
    val lineColor = MaterialTheme.colorScheme.primary
    val textColor = MaterialTheme.colorScheme.onSurface
    
    Column(modifier = modifier) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            drawRevenueLine(
                data = data,
                maxValue = maxValue,
                minValue = minValue,
                lineColor = lineColor
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Labels
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            data.forEach { item ->
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = item.day,
                        style = MaterialTheme.typography.labelSmall,
                        color = textColor
                    )
                    Text(
                        text = "$${item.amount.toInt()}",
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Bold,
                        color = textColor
                    )
                }
            }
        }
    }
}

/**
 * Draw revenue line on canvas
 */
private fun DrawScope.drawRevenueLine(
    data: List<RevenueData>,
    maxValue: Double,
    minValue: Double,
    lineColor: Color
) {
    if (data.size < 2) return
    
    val path = Path()
    val stepX = size.width / (data.size - 1)
    val range = maxValue - minValue
    val chartHeight = size.height * 0.8f
    
    data.forEachIndexed { index, item ->
        val x = index * stepX
        val normalizedValue = if (range > 0) (item.amount - minValue) / range else 0.5
        val y = size.height - (normalizedValue * chartHeight).toFloat() - (size.height - chartHeight) / 2
        
        if (index == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
        
        // Draw point
        drawCircle(
            color = lineColor,
            radius = 4.dp.toPx(),
            center = Offset(x, y)
        )
    }
    
    // Draw line
    drawPath(
        path = path,
        color = lineColor,
        style = Stroke(width = 3.dp.toPx())
    )
}

@Preview(showBackground = true)
@Composable
fun RevenueChartPreview() {
    BakeryTrackerTheme {
        RevenueChart(
            data = listOf(
                RevenueData("Mon", 850.0),
                RevenueData("Tue", 920.0),
                RevenueData("Wed", 1100.0),
                RevenueData("Thu", 980.0),
                RevenueData("Fri", 1250.0),
                RevenueData("Sat", 1400.0),
                RevenueData("Sun", 1150.0)
            ),
            modifier = Modifier.height(200.dp)
        )
    }
}
