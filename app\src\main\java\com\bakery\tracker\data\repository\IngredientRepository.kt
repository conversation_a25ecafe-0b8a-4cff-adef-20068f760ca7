package com.bakery.tracker.data.repository

import com.bakery.tracker.data.local.dao.IngredientDao
import com.bakery.tracker.data.models.Ingredient
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing ingredient data operations
 */
@Singleton
class IngredientRepository @Inject constructor(
    private val ingredientDao: IngredientDao
) {
    
    /**
     * Get all ingredients as a Flow
     */
    fun getAllIngredients(): Flow<List<Ingredient>> = ingredientDao.getAllIngredients()
    
    /**
     * Get all active ingredients
     */
    fun getActiveIngredients(): Flow<List<Ingredient>> = ingredientDao.getActiveIngredients()
    
    /**
     * Get ingredients with low stock
     */
    fun getLowStockIngredients(): Flow<List<Ingredient>> = ingredientDao.getLowStockIngredients()
    
    /**
     * Get ingredient by ID
     */
    suspend fun getIngredientById(id: Long): Ingredient? = ingredientDao.getIngredientById(id)
    
    /**
     * Get ingredient by name
     */
    suspend fun getIngredientByName(name: String): Ingredient? = ingredientDao.getIngredientByName(name)
    
    /**
     * Search ingredients by name
     */
    fun searchIngredients(searchQuery: String): Flow<List<Ingredient>> = 
        ingredientDao.searchIngredients(searchQuery)
    
    /**
     * Insert a new ingredient
     */
    suspend fun insertIngredient(ingredient: Ingredient): Long = ingredientDao.insertIngredient(ingredient)
    
    /**
     * Insert multiple ingredients
     */
    suspend fun insertIngredients(ingredients: List<Ingredient>): List<Long> = 
        ingredientDao.insertIngredients(ingredients)
    
    /**
     * Update an existing ingredient
     */
    suspend fun updateIngredient(ingredient: Ingredient) = ingredientDao.updateIngredient(ingredient)
    
    /**
     * Update ingredient stock
     */
    suspend fun updateIngredientStock(id: Long, newStock: Double) = 
        ingredientDao.updateIngredientStock(id, newStock)
    
    /**
     * Update ingredient price
     */
    suspend fun updateIngredientPrice(id: Long, newPrice: Double) = 
        ingredientDao.updateIngredientPrice(id, newPrice)
    
    /**
     * Decrease ingredient stock (for production)
     * Returns the number of rows affected (0 if insufficient stock)
     */
    suspend fun decreaseStock(id: Long, amount: Double): Boolean {
        val rowsAffected = ingredientDao.decreaseStock(id, amount)
        return rowsAffected > 0
    }
    
    /**
     * Increase ingredient stock (for restocking)
     */
    suspend fun increaseStock(id: Long, amount: Double) = ingredientDao.increaseStock(id, amount)
    
    /**
     * Delete an ingredient
     */
    suspend fun deleteIngredient(ingredient: Ingredient) = ingredientDao.deleteIngredient(ingredient)
    
    /**
     * Delete ingredient by ID
     */
    suspend fun deleteIngredientById(id: Long) = ingredientDao.deleteIngredientById(id)
    
    /**
     * Get total count of ingredients
     */
    suspend fun getIngredientCount(): Int = ingredientDao.getIngredientCount()
    
    /**
     * Get total value of all ingredients in stock
     */
    suspend fun getTotalStockValue(): Double = ingredientDao.getTotalStockValue() ?: 0.0
    
    /**
     * Check if ingredient has sufficient stock for given amount
     */
    suspend fun hasSufficientStock(ingredientId: Long, requiredAmount: Double): Boolean {
        val ingredient = getIngredientById(ingredientId)
        return ingredient?.currentStock?.let { it >= requiredAmount } ?: false
    }
    
    /**
     * Get ingredients that need restocking (below threshold)
     */
    suspend fun getIngredientsNeedingRestock(): List<Ingredient> {
        return getAllIngredients().let { flow ->
            // This is a simplified version - in real implementation you might want to collect the flow
            emptyList()
        }
    }
}
