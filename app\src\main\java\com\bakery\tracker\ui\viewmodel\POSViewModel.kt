package com.bakery.tracker.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bakery.tracker.data.models.*
import com.bakery.tracker.data.repository.CartRepository
import com.bakery.tracker.data.repository.ProductRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

/**
 * ViewModel for POS (Point of Sale) screen
 * Implements shopping cart functionality as described in overview.txt
 */
@HiltViewModel
class POSViewModel @Inject constructor(
    private val productRepository: ProductRepository,
    private val cartRepository: CartRepository
) : ViewModel() {
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery = _searchQuery.asStateFlow()
    
    private val _selectedCategory = MutableStateFlow<ProductCategory?>(null)
    val selectedCategory = _selectedCategory.asStateFlow()
    
    private val sessionId = UUID.randomUUID().toString()
    
    // Get all products
    private val allProducts = productRepository.getActiveProducts()
    
    // Get popular products (marked as popular)
    private val popularProducts = allProducts.map { products ->
        products.filter { it.isPopular }
    }
    
    // Get cart items for current session
    private val cartItems = cartRepository.getCartItems(sessionId)
    
    // Calculate cart summary
    private val cartSummary = cartItems.map { items ->
        CartSummary.fromCartItems(items)
    }
    
    // Filter products based on search and category
    private val filteredProducts = combine(
        allProducts,
        searchQuery,
        selectedCategory
    ) { products, query, category ->
        products.filter { product ->
            val matchesSearch = if (query.isBlank()) {
                true
            } else {
                product.name.contains(query, ignoreCase = true) ||
                product.description.contains(query, ignoreCase = true)
            }
            
            val matchesCategory = category?.let { product.category == it } ?: true
            
            matchesSearch && matchesCategory
        }
    }
    
    // Combine all state into UI state
    val uiState = combine(
        filteredProducts,
        popularProducts,
        cartItems,
        cartSummary,
        searchQuery,
        selectedCategory
    ) { filtered, popular, cart, summary, query, category ->
        POSUiState(
            filteredProducts = filtered,
            popularProducts = popular,
            cartItems = cart,
            cartSummary = summary,
            searchQuery = query,
            selectedCategory = category
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = POSUiState()
    )
    
    /**
     * Update search query
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    /**
     * Select category filter
     */
    fun selectCategory(category: ProductCategory?) {
        _selectedCategory.value = category
    }
    
    /**
     * Add product to cart
     * Implements: cart functionality from overview.txt
     */
    fun addToCart(product: Product) {
        viewModelScope.launch {
            // Check if product already exists in cart
            val existingItem = cartItems.value.find { it.productId == product.id }
            
            if (existingItem != null) {
                // Update quantity
                cartRepository.updateCartItemQuantity(
                    existingItem.id,
                    existingItem.quantity + 1
                )
            } else {
                // Add new item
                val cartItem = CartItem(
                    productId = product.id,
                    productName = product.name,
                    price = product.sellingPrice,
                    quantity = 1,
                    category = product.category,
                    isPopular = product.isPopular,
                    sessionId = sessionId
                )
                cartRepository.addToCart(cartItem)
            }
        }
    }
    
    /**
     * Update cart item quantity
     */
    fun updateCartItemQuantity(cartItemId: Long, newQuantity: Int) {
        viewModelScope.launch {
            if (newQuantity <= 0) {
                cartRepository.removeFromCart(cartItemId)
            } else {
                cartRepository.updateCartItemQuantity(cartItemId, newQuantity)
            }
        }
    }
    
    /**
     * Remove item from cart
     */
    fun removeFromCart(cartItemId: Long) {
        viewModelScope.launch {
            cartRepository.removeFromCart(cartItemId)
        }
    }
    
    /**
     * Clear entire cart
     */
    fun clearCart() {
        viewModelScope.launch {
            cartRepository.clearCart(sessionId)
        }
    }
    
    /**
     * Proceed to checkout
     * This would navigate to checkout screen in full implementation
     */
    fun proceedToCheckout() {
        // TODO: Navigate to checkout screen
        // For now, this is a placeholder
    }
}

/**
 * UI state for POS screen
 */
data class POSUiState(
    val filteredProducts: List<Product> = emptyList(),
    val popularProducts: List<Product> = emptyList(),
    val cartItems: List<CartItem> = emptyList(),
    val cartSummary: CartSummary = CartSummary(emptyList(), 0.0, 0, 0),
    val searchQuery: String = "",
    val selectedCategory: ProductCategory? = null,
    val isLoading: Boolean = false,
    val error: String? = null
)
