package com.bakery.tracker.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bakery.tracker.data.models.*
import com.bakery.tracker.data.repository.RecipeRepository
import com.bakery.tracker.domain.usecase.AddRecipeUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for Recipe Management screen
 * Implements recipe features as described in overview.txt
 */
@HiltViewModel
class RecipeViewModel @Inject constructor(
    private val recipeRepository: RecipeRepository,
    private val addRecipeUseCase: AddRecipeUseCase
) : ViewModel() {
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery = _searchQuery.asStateFlow()
    
    private val _selectedCategory = MutableStateFlow<ProductCategory?>(null)
    val selectedCategory = _selectedCategory.asStateFlow()
    
    // Get all recipes
    private val allRecipes = recipeRepository.getAllRecipes()
    
    // Filter recipes based on search and category
    private val filteredRecipes = combine(
        allRecipes,
        searchQuery,
        selectedCategory
    ) { recipes, query, category ->
        recipes.filter { recipe ->
            val matchesSearch = if (query.isBlank()) {
                true
            } else {
                recipe.name.contains(query, ignoreCase = true) ||
                recipe.instructions.contains(query, ignoreCase = true)
            }
            
            val matchesCategory = category?.let { recipe.category == it } ?: true
            
            matchesSearch && matchesCategory && recipe.isActive
        }
    }
    
    // Combine all state into UI state
    val uiState = combine(
        filteredRecipes,
        searchQuery,
        selectedCategory
    ) { filtered, query, category ->
        RecipeUiState(
            filteredRecipes = filtered,
            searchQuery = query,
            selectedCategory = category
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = RecipeUiState()
    )
    
    /**
     * Update search query
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    /**
     * Select category filter
     */
    fun selectCategory(category: ProductCategory?) {
        _selectedCategory.value = category
    }
    
    /**
     * Add new recipe with ingredients
     */
    fun addRecipe(recipe: Recipe, ingredients: List<RecipeIngredient>) {
        viewModelScope.launch {
            addRecipeUseCase(recipe, ingredients)
        }
    }
    
    /**
     * Edit existing recipe
     */
    fun editRecipe(recipe: Recipe) {
        // TODO: Navigate to edit recipe screen
        // For now, this is a placeholder
    }
    
    /**
     * Delete recipe
     */
    fun deleteRecipe(recipe: Recipe) {
        viewModelScope.launch {
            recipeRepository.deleteRecipe(recipe)
        }
    }
    
    /**
     * Toggle recipe active status
     */
    fun toggleRecipeActive(recipe: Recipe) {
        viewModelScope.launch {
            recipeRepository.toggleRecipeActiveStatus(recipe.id, !recipe.isActive)
        }
    }
}

/**
 * UI state for Recipe screen
 */
data class RecipeUiState(
    val filteredRecipes: List<Recipe> = emptyList(),
    val searchQuery: String = "",
    val selectedCategory: ProductCategory? = null,
    val isLoading: Boolean = false,
    val error: String? = null
)
