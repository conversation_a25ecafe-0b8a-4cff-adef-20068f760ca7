package com.bakery.tracker.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bakery.tracker.data.preferences.PreferencesManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for managing authentication state
 */
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val preferencesManager: PreferencesManager
) : ViewModel() {
    
    /**
     * StateFlow for login status
     */
    val isLoggedIn: StateFlow<Boolean> = preferencesManager.userToken
        .map { token -> token != null }
        .stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = false
        )
    
    /**
     * StateFlow for user role
     */
    val userRole: StateFlow<String> = preferencesManager.userRole
        .stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = "staff"
        )
    
    /**
     * StateFlow for user name
     */
    val userName: StateFlow<String> = preferencesManager.userName
        .stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = ""
        )
    
    /**
     * Mock login function with exact credentials from overview.txt
     */
    fun login(email: String, password: String): Boolean {
        val isValidCredentials = when {
            email == "<EMAIL>" && password == "admin123" -> {
                viewModelScope.launch {
                    val mockToken = "mock_jwt_token_${System.currentTimeMillis()}"
                    preferencesManager.setUserToken(mockToken)
                    preferencesManager.setUserName("Admin User")
                    preferencesManager.setUserRole("admin")
                }
                true
            }
            email == "<EMAIL>" && password == "staff123" -> {
                viewModelScope.launch {
                    val mockToken = "mock_jwt_token_${System.currentTimeMillis()}"
                    preferencesManager.setUserToken(mockToken)
                    preferencesManager.setUserName("Staff User")
                    preferencesManager.setUserRole("staff")
                }
                true
            }
            else -> false
        }
        return isValidCredentials
    }
    
    /**
     * Logout function
     */
    fun logout() {
        viewModelScope.launch {
            preferencesManager.clearUserData()
        }
    }
    
    /**
     * Toggle user role (for demo purposes)
     */
    fun toggleUserRole() {
        viewModelScope.launch {
            val currentRole = userRole.value
            val newRole = if (currentRole == "admin") "staff" else "admin"
            preferencesManager.setUserRole(newRole)
        }
    }
}
