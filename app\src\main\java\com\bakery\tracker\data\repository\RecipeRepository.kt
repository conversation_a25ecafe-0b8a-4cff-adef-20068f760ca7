package com.bakery.tracker.data.repository

import com.bakery.tracker.data.local.BakeryDatabase
import com.bakery.tracker.data.local.dao.RecipeDao
import com.bakery.tracker.data.local.dao.RecipeIngredientWithDetails
import com.bakery.tracker.data.models.Recipe
import com.bakery.tracker.data.models.RecipeIngredient
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing recipe data operations
 */
@Singleton
class RecipeRepository @Inject constructor(
    private val recipeDao: RecipeDao,
    private val database: BakeryDatabase
) {
    
    /**
     * Get all recipes as a Flow
     */
    fun getAllRecipes(): Flow<List<Recipe>> = recipeDao.getAllRecipes()
    
    /**
     * Get all active recipes
     */
    fun getActiveRecipes(): Flow<List<Recipe>> = recipeDao.getActiveRecipes()
    
    /**
     * Get recipes for a specific product
     */
    fun getRecipesForProduct(productId: Long): Flow<List<Recipe>> = 
        recipeDao.getRecipesForProduct(productId)
    
    /**
     * Get recipe by ID
     */
    suspend fun getRecipeById(id: Long): Recipe? = recipeDao.getRecipeById(id)
    
    /**
     * Get recipe by name
     */
    suspend fun getRecipeByName(name: String): Recipe? = recipeDao.getRecipeByName(name)
    
    /**
     * Search recipes by name
     */
    fun searchRecipes(searchQuery: String): Flow<List<Recipe>> = 
        recipeDao.searchRecipes(searchQuery)
    
    /**
     * Insert a new recipe
     */
    suspend fun insertRecipe(recipe: Recipe): Long = recipeDao.insertRecipe(recipe)
    
    /**
     * Insert a complete recipe with ingredients in a transaction
     */
    suspend fun insertRecipeWithIngredients(
        recipe: Recipe, 
        ingredients: List<RecipeIngredient>
    ): Long {
        return database.runInTransaction {
            val recipeId = recipeDao.insertRecipe(recipe)
            val recipeIngredients = ingredients.map { it.copy(recipeId = recipeId) }
            recipeDao.insertRecipeIngredients(recipeIngredients)
            recipeId
        }
    }
    
    /**
     * Update an existing recipe
     */
    suspend fun updateRecipe(recipe: Recipe) = recipeDao.updateRecipe(recipe)
    
    /**
     * Update recipe with ingredients in a transaction
     */
    suspend fun updateRecipeWithIngredients(
        recipe: Recipe,
        ingredients: List<RecipeIngredient>
    ) {
        database.runInTransaction {
            recipeDao.updateRecipe(recipe)
            recipeDao.deleteAllRecipeIngredients(recipe.id)
            val recipeIngredients = ingredients.map { it.copy(recipeId = recipe.id) }
            recipeDao.insertRecipeIngredients(recipeIngredients)
        }
    }
    
    /**
     * Toggle recipe active status
     */
    suspend fun toggleRecipeActiveStatus(id: Long, isActive: Boolean) = 
        recipeDao.toggleRecipeActiveStatus(id, isActive)
    
    /**
     * Delete a recipe
     */
    suspend fun deleteRecipe(recipe: Recipe) = recipeDao.deleteRecipe(recipe)
    
    /**
     * Delete recipe by ID
     */
    suspend fun deleteRecipeById(id: Long) = recipeDao.deleteRecipeById(id)
    
    // Recipe Ingredients operations
    
    /**
     * Get all ingredients for a recipe
     */
    suspend fun getRecipeIngredients(recipeId: Long): List<RecipeIngredient> = 
        recipeDao.getRecipeIngredients(recipeId)
    
    /**
     * Get recipe ingredients with ingredient details
     */
    fun getRecipeIngredientsWithDetails(recipeId: Long): Flow<List<RecipeIngredientWithDetails>> = 
        recipeDao.getRecipeIngredientsWithDetails(recipeId)
    
    /**
     * Insert recipe ingredient
     */
    suspend fun insertRecipeIngredient(recipeIngredient: RecipeIngredient): Long = 
        recipeDao.insertRecipeIngredient(recipeIngredient)
    
    /**
     * Update recipe ingredient
     */
    suspend fun updateRecipeIngredient(recipeIngredient: RecipeIngredient) = 
        recipeDao.updateRecipeIngredient(recipeIngredient)
    
    /**
     * Delete recipe ingredient
     */
    suspend fun deleteRecipeIngredient(recipeIngredient: RecipeIngredient) = 
        recipeDao.deleteRecipeIngredient(recipeIngredient)
    
    /**
     * Get total count of active recipes
     */
    suspend fun getActiveRecipeCount(): Int = recipeDao.getActiveRecipeCount()
    
    /**
     * Calculate recipe cost based on current ingredient prices
     */
    suspend fun calculateRecipeCost(recipeId: Long): Double {
        val ingredientsWithDetails = recipeDao.getRecipeIngredientsWithDetails(recipeId)
        // This would need to be collected from Flow in real implementation
        // For now, return 0.0 as placeholder
        return 0.0
    }
    
    /**
     * Check if recipe can be made with current stock
     */
    suspend fun canMakeRecipe(recipeId: Long, quantity: Int = 1): Boolean {
        val ingredients = getRecipeIngredients(recipeId)
        // Check if all ingredients have sufficient stock
        // This would involve checking against ingredient repository
        return true // Placeholder implementation
    }
    
    /**
     * Get missing ingredients for a recipe
     */
    suspend fun getMissingIngredients(recipeId: Long, quantity: Int = 1): List<RecipeIngredient> {
        // Return ingredients that don't have sufficient stock
        return emptyList() // Placeholder implementation
    }
}
