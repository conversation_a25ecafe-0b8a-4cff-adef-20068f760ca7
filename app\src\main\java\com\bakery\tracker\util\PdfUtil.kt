package com.bakery.tracker.util

import android.content.Context
import com.bakery.tracker.data.models.Bill
import com.bakery.tracker.data.models.BillItem
import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.PdfWriter
import com.itextpdf.layout.Document
import com.itextpdf.layout.element.Paragraph
import com.itextpdf.layout.element.Table
import com.itextpdf.layout.property.TextAlignment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for PDF generation operations
 */
@Singleton
class PdfUtil @Inject constructor() {
    
    /**
     * Generate PDF invoice for a bill
     */
    suspend fun generateInvoicePdf(
        context: Context,
        bill: Bill,
        billItems: List<BillItem>,
        outputFile: File
    ): Result<File> = withContext(Dispatchers.IO) {
        try {
            FileOutputStream(outputFile).use { fos ->
                val pdfWriter = PdfWriter(fos)
                val pdfDocument = PdfDocument(pdfWriter)
                val document = Document(pdfDocument)
                
                // Add header
                addInvoiceHeader(document, bill)
                
                // Add customer information
                addCustomerInfo(document, bill)
                
                // Add items table
                addItemsTable(document, billItems)
                
                // Add totals
                addTotals(document, bill)
                
                // Add footer
                addInvoiceFooter(document, bill)
                
                document.close()
            }
            
            Result.success(outputFile)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Add invoice header
     */
    private fun addInvoiceHeader(document: Document, bill: Bill) {
        // Company name
        document.add(
            Paragraph("Bakery Tracker Pro")
                .setFontSize(20f)
                .setBold()
                .setTextAlignment(TextAlignment.CENTER)
        )
        
        // Invoice title
        document.add(
            Paragraph("INVOICE")
                .setFontSize(16f)
                .setBold()
                .setTextAlignment(TextAlignment.CENTER)
        )
        
        // Invoice number and date
        val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
        document.add(
            Paragraph("Invoice #: ${bill.billNumber}")
                .setFontSize(12f)
        )
        document.add(
            Paragraph("Date: ${dateFormat.format(Date(bill.createdAt))}")
                .setFontSize(12f)
        )
        
        document.add(Paragraph("\n"))
    }
    
    /**
     * Add customer information
     */
    private fun addCustomerInfo(document: Document, bill: Bill) {
        document.add(
            Paragraph("Bill To:")
                .setFontSize(12f)
                .setBold()
        )
        
        document.add(
            Paragraph(bill.customerName)
                .setFontSize(11f)
        )
        
        if (bill.customerPhone.isNotEmpty()) {
            document.add(
                Paragraph("Phone: ${bill.customerPhone}")
                    .setFontSize(11f)
            )
        }
        
        if (bill.customerEmail.isNotEmpty()) {
            document.add(
                Paragraph("Email: ${bill.customerEmail}")
                    .setFontSize(11f)
            )
        }
        
        document.add(Paragraph("\n"))
    }
    
    /**
     * Add items table
     */
    private fun addItemsTable(document: Document, billItems: List<BillItem>) {
        val table = Table(floatArrayOf(3f, 1f, 1f, 1f))
        table.setWidth(com.itextpdf.layout.property.UnitValue.createPercentValue(100f))
        
        // Table headers
        table.addHeaderCell("Item")
        table.addHeaderCell("Qty")
        table.addHeaderCell("Price")
        table.addHeaderCell("Total")
        
        // Table rows
        billItems.forEach { item ->
            table.addCell(item.productName)
            table.addCell(item.quantity.toString())
            table.addCell(String.format("$%.2f", item.unitPrice))
            table.addCell(String.format("$%.2f", item.totalPrice))
        }
        
        document.add(table)
        document.add(Paragraph("\n"))
    }
    
    /**
     * Add totals section
     */
    private fun addTotals(document: Document, bill: Bill) {
        val totalsTable = Table(floatArrayOf(3f, 1f))
        totalsTable.setWidth(com.itextpdf.layout.property.UnitValue.createPercentValue(50f))
        
        // Subtotal
        totalsTable.addCell("Subtotal:")
        totalsTable.addCell(String.format("$%.2f", bill.subtotal))
        
        // Discount
        if (bill.discountAmount > 0) {
            totalsTable.addCell("Discount:")
            totalsTable.addCell(String.format("-$%.2f", bill.discountAmount))
        }
        
        // Tax
        if (bill.taxAmount > 0) {
            totalsTable.addCell("Tax:")
            totalsTable.addCell(String.format("$%.2f", bill.taxAmount))
        }
        
        // Total
        totalsTable.addCell("Total:")
        totalsTable.addCell(
            Paragraph(String.format("$%.2f", bill.totalAmount))
                .setBold()
        )
        
        document.add(totalsTable)
        document.add(Paragraph("\n"))
    }
    
    /**
     * Add invoice footer
     */
    private fun addInvoiceFooter(document: Document, bill: Bill) {
        // Payment status
        document.add(
            Paragraph("Payment Status: ${bill.paymentStatus}")
                .setFontSize(11f)
        )
        
        // Payment method
        document.add(
            Paragraph("Payment Method: ${bill.paymentMethod}")
                .setFontSize(11f)
        )
        
        // QR code placeholder (in real implementation, you'd generate actual QR code)
        document.add(Paragraph("\n"))
        document.add(
            Paragraph("QR Code: [Bill ID: ${bill.id}]")
                .setFontSize(10f)
                .setTextAlignment(TextAlignment.CENTER)
        )
        
        // Thank you message
        document.add(Paragraph("\n"))
        document.add(
            Paragraph("Thank you for your business!")
                .setFontSize(12f)
                .setTextAlignment(TextAlignment.CENTER)
        )
    }
    
    /**
     * Generate production report PDF
     */
    suspend fun generateProductionReportPdf(
        context: Context,
        startDate: Date,
        endDate: Date,
        outputFile: File
    ): Result<File> = withContext(Dispatchers.IO) {
        try {
            // TODO: Implement production report PDF generation
            // This is a placeholder implementation
            
            FileOutputStream(outputFile).use { fos ->
                val pdfWriter = PdfWriter(fos)
                val pdfDocument = PdfDocument(pdfWriter)
                val document = Document(pdfDocument)
                
                document.add(
                    Paragraph("Production Report")
                        .setFontSize(20f)
                        .setBold()
                        .setTextAlignment(TextAlignment.CENTER)
                )
                
                val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
                document.add(
                    Paragraph("Period: ${dateFormat.format(startDate)} - ${dateFormat.format(endDate)}")
                        .setFontSize(12f)
                        .setTextAlignment(TextAlignment.CENTER)
                )
                
                document.add(Paragraph("\nReport content will be implemented here."))
                
                document.close()
            }
            
            Result.success(outputFile)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
