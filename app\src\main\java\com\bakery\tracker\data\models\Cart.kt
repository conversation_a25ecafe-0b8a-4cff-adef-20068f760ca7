package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * Represents a shopping cart item for the POS system
 * As described in overview.txt - Shopping Cart Screen
 * 
 * @param id Unique identifier for the cart item
 * @param productId ID of the product
 * @param productName Name of the product (cached for performance)
 * @param price Price per unit at time of adding to cart
 * @param quantity Quantity selected
 * @param category Product category
 * @param isPopular Whether this is a popular item
 * @param sessionId Session identifier to group cart items
 * @param createdAt Timestamp when item was added to cart
 */
@Entity(
    tableName = "cart_items",
    foreignKeys = [
        ForeignKey(
            entity = Product::class,
            parentColumns = ["id"],
            childColumns = ["productId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["productId"]),
        Index(value = ["sessionId"])
    ]
)
data class CartItem(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val productId: Long,
    val productName: String,
    val price: Double,
    val quantity: Int,
    val category: ProductCategory,
    val isPopular: Boolean = false,
    val sessionId: String,
    val createdAt: Long = System.currentTimeMillis()
) {
    /**
     * Calculates total price for this cart item
     * cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0)
     */
    fun getTotalPrice(): Double = price * quantity
}

/**
 * Represents customer information for billing
 * As described in overview.txt - Customer Information section
 */
@Entity(tableName = "customers")
data class Customer(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val phoneNumber: String,
    val email: String = "",
    val address: String = "",
    val totalOrders: Int = 0,
    val totalSpent: Double = 0.0,
    val lastOrderDate: Long? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * Represents a complete order/bill
 * As described in overview.txt - Billing Features
 */
@Entity(
    tableName = "orders",
    foreignKeys = [
        ForeignKey(
            entity = Customer::class,
            parentColumns = ["id"],
            childColumns = ["customerId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [
        Index(value = ["customerId"]),
        Index(value = ["orderDate"]),
        Index(value = ["billNumber"])
    ]
)
data class Order(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val billNumber: String, // Unique bill ID
    val customerId: Long? = null,
    val customerName: String,
    val customerPhone: String,
    val subtotal: Double,
    val discount: Double = 0.0,
    val tax: Double = 0.0,
    val total: Double,
    val paymentMethod: PaymentMethod,
    val paymentStatus: OrderStatus = OrderStatus.PENDING,
    val orderDate: Long = System.currentTimeMillis(),
    val notes: String = ""
) {
    /**
     * Calculates final total with discount and tax
     */
    fun calculateTotal(): Double = subtotal - discount + tax
}

/**
 * Represents an item in an order
 */
@Entity(
    tableName = "order_items",
    foreignKeys = [
        ForeignKey(
            entity = Order::class,
            parentColumns = ["id"],
            childColumns = ["orderId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = Product::class,
            parentColumns = ["id"],
            childColumns = ["productId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["orderId"]),
        Index(value = ["productId"])
    ]
)
data class OrderItem(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val orderId: Long,
    val productId: Long,
    val productName: String,
    val price: Double,
    val quantity: Int,
    val total: Double
) {
    /**
     * Calculates total for this order item
     */
    fun calculateTotal(): Double = price * quantity
}

/**
 * Payment methods as described in overview.txt
 */
enum class PaymentMethod(val displayName: String) {
    CASH("Cash"),
    CARD("Card"),
    UPI("UPI")
}

/**
 * Order status
 */
enum class OrderStatus(val displayName: String) {
    PENDING("Pending"),
    COMPLETED("Completed"),
    CANCELLED("Cancelled")
}

/**
 * Cart summary for calculations
 */
data class CartSummary(
    val items: List<CartItem>,
    val subtotal: Double,
    val itemCount: Int,
    val uniqueProductCount: Int
) {
    companion object {
        /**
         * Creates cart summary from cart items
         * Implements: cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0)
         */
        fun fromCartItems(items: List<CartItem>): CartSummary {
            val subtotal = items.sumOf { it.getTotalPrice() }
            val itemCount = items.sumOf { it.quantity }
            val uniqueProductCount = items.distinctBy { it.productId }.size
            
            return CartSummary(
                items = items,
                subtotal = subtotal,
                itemCount = itemCount,
                uniqueProductCount = uniqueProductCount
            )
        }
    }
}
