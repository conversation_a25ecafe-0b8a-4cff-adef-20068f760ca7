package com.bakery.tracker.ui.navigation

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.bakery.tracker.ui.screens.*
import com.bakery.tracker.ui.viewmodel.AuthViewModel

/**
 * Navigation destinations for the app
 */
sealed class Screen(val route: String, val title: String, val icon: ImageVector) {
    object Login : Screen("login", "Login", Icons.Default.Login)
    object Dashboard : Screen("dashboard", "Dashboard", Icons.Default.Dashboard)
    object POS : Screen("pos", "POS", Icons.Default.Store)
    object Recipes : Screen("recipes", "Recipes", Icons.Default.MenuBook)
    object Production : Screen("production", "Production", Icons.Default.Factory)
    object Stock : Screen("stock", "Stock", Icons.Default.Inventory)
    object Pricing : Screen("pricing", "Pricing", Icons.Default.AttachMoney)
    object BillCapture : Screen("bill_capture", "Bills", Icons.Default.Receipt)
    object Orders : Screen("orders", "Orders", Icons.Default.ShoppingCart)
    object Staff : Screen("staff", "Staff", Icons.Default.People)
    object Settings : Screen("settings", "Settings", Icons.Default.Settings)
}

/**
 * Bottom navigation items (excluding login and admin-only screens)
 */
val bottomNavItems = listOf(
    Screen.Dashboard,
    Screen.POS,
    Screen.Recipes,
    Screen.Production,
    Screen.Stock
)

/**
 * Admin-only screens
 */
val adminOnlyScreens = listOf(
    Screen.Orders,
    Screen.Pricing,
    Screen.Staff,
    Screen.Settings
)

/**
 * Main navigation composable
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BakeryTrackerNavigation(
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val isLoggedIn by authViewModel.isLoggedIn.collectAsState()
    val userRole by authViewModel.userRole.collectAsState()
    val isAdmin = userRole == "admin"
    
    if (!isLoggedIn) {
        // Show login screen if not logged in
        LoginScreen(
            onLoginSuccess = {
                navController.navigate(Screen.Dashboard.route) {
                    popUpTo(Screen.Login.route) { inclusive = true }
                }
            }
        )
    } else {
        // Show main app with bottom navigation
        Scaffold(
            modifier = modifier,
            bottomBar = {
                NavigationBar {
                    val navBackStackEntry by navController.currentBackStackEntryAsState()
                    val currentDestination = navBackStackEntry?.destination
                    
                    bottomNavItems.forEach { screen ->
                        NavigationBarItem(
                            icon = { Icon(screen.icon, contentDescription = screen.title) },
                            label = { Text(screen.title) },
                            selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                            onClick = {
                                navController.navigate(screen.route) {
                                    popUpTo(navController.graph.findStartDestination().id) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true
                                }
                            }
                        )
                    }
                    
                    // Add admin-only items if user is admin
                    if (isAdmin) {
                        adminOnlyScreens.forEach { screen ->
                            NavigationBarItem(
                                icon = { Icon(screen.icon, contentDescription = screen.title) },
                                label = { Text(screen.title) },
                                selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                                onClick = {
                                    navController.navigate(screen.route) {
                                        popUpTo(navController.graph.findStartDestination().id) {
                                            saveState = true
                                        }
                                        launchSingleTop = true
                                        restoreState = true
                                    }
                                }
                            )
                        }
                    }
                }
            }
        ) { innerPadding ->
            NavHost(
                navController = navController,
                startDestination = Screen.Dashboard.route,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
            ) {
                composable(Screen.Dashboard.route) {
                    DashboardScreen()
                }
                composable(Screen.POS.route) {
                    POSScreen()
                }
                composable(Screen.Recipes.route) {
                    RecipeScreen()
                }
                composable(Screen.Production.route) {
                    ProductionScreen()
                }
                composable(Screen.Stock.route) {
                    StockScreen()
                }
                composable(Screen.BillCapture.route) {
                    BillCaptureScreen()
                }
                
                // Admin-only screens
                if (isAdmin) {
                    composable(Screen.Orders.route) {
                        OrderScreen()
                    }
                    composable(Screen.Pricing.route) {
                        PricingScreen()
                    }
                    composable(Screen.Staff.route) {
                        StaffScreen()
                    }
                    composable(Screen.Settings.route) {
                        SettingsScreen()
                    }
                }
            }
        }
    }
}
