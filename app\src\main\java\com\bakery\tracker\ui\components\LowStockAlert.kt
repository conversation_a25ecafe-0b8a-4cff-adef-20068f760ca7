package com.bakery.tracker.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.bakery.tracker.data.models.Ingredient
import com.bakery.tracker.ui.theme.BakeryTrackerTheme

/**
 * Component for displaying low stock alerts
 */
@Composable
fun LowStockAlert(
    ingredient: Ingredient,
    onRestockClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = ingredient.name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
                
                Text(
                    text = "Current: ${ingredient.currentStock} ${ingredient.unit}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
                
                Text(
                    text = "Threshold: ${ingredient.lowStockThreshold} ${ingredient.unit}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
            
            FilledTonalButton(
                onClick = onRestockClick,
                colors = ButtonDefaults.filledTonalButtonColors(
                    containerColor = MaterialTheme.colorScheme.error,
                    contentColor = MaterialTheme.colorScheme.onError
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Restock",
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("Restock")
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun LowStockAlertPreview() {
    BakeryTrackerTheme {
        LowStockAlert(
            ingredient = Ingredient(
                id = 1,
                name = "Flour",
                unit = "kg",
                currentStock = 5.0,
                lowStockThreshold = 10.0,
                lastPaidPrice = 2.50
            ),
            onRestockClick = {}
        )
    }
}
