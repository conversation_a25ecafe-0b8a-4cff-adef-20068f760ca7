package com.bakery.tracker.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.bakery.tracker.ui.theme.BakeryTrackerTheme
import com.bakery.tracker.ui.theme.ChartColors
import com.bakery.tracker.ui.viewmodel.ProductionData
import kotlin.math.max

/**
 * Simple bar chart component for production data
 */
@Composable
fun ProductionChart(
    data: List<ProductionData>,
    modifier: Modifier = Modifier
) {
    if (data.isEmpty()) {
        Box(
            modifier = modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "No production data available",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        return
    }
    
    val maxValue = data.maxOfOrNull { it.quantity } ?: 1
    val barColor = MaterialTheme.colorScheme.primary
    val textColor = MaterialTheme.colorScheme.onSurface
    
    Column(modifier = modifier) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            drawProductionBars(
                data = data,
                maxValue = maxValue,
                barColor = barColor,
                canvasSize = size
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Labels
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            data.forEach { item ->
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = item.product,
                        style = MaterialTheme.typography.labelSmall,
                        color = textColor
                    )
                    Text(
                        text = "${item.quantity}",
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Bold,
                        color = textColor
                    )
                }
            }
        }
    }
}

/**
 * Draw production bars on canvas
 */
private fun DrawScope.drawProductionBars(
    data: List<ProductionData>,
    maxValue: Int,
    barColor: Color,
    canvasSize: Size
) {
    val barWidth = canvasSize.width / data.size * 0.7f
    val barSpacing = canvasSize.width / data.size * 0.3f
    val maxBarHeight = canvasSize.height * 0.8f
    
    data.forEachIndexed { index, item ->
        val barHeight = (item.quantity.toFloat() / maxValue) * maxBarHeight
        val x = index * (barWidth + barSpacing) + barSpacing / 2
        val y = canvasSize.height - barHeight
        
        // Draw bar
        drawRect(
            color = barColor,
            topLeft = Offset(x, y),
            size = Size(barWidth, barHeight)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ProductionChartPreview() {
    BakeryTrackerTheme {
        ProductionChart(
            data = listOf(
                ProductionData("Bread", 45),
                ProductionData("Cakes", 23),
                ProductionData("Pastries", 67),
                ProductionData("Cookies", 21)
            ),
            modifier = Modifier.height(200.dp)
        )
    }
}
