package com.bakery.tracker.data.local.dao

import androidx.room.*
import com.bakery.tracker.data.models.*
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for Cart operations
 * Implements POS system functionality as described in overview.txt
 */
@Dao
interface CartDao {
    
    // Cart Items operations
    
    /**
     * Get all cart items for current session
     */
    @Query("SELECT * FROM cart_items WHERE sessionId = :sessionId ORDER BY createdAt ASC")
    fun getCartItems(sessionId: String): Flow<List<CartItem>>
    
    /**
     * Add item to cart
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun addToCart(cartItem: CartItem): Long
    
    /**
     * Update cart item quantity
     */
    @Query("UPDATE cart_items SET quantity = :quantity WHERE id = :cartItemId")
    suspend fun updateCartItemQuantity(cartItemId: Long, quantity: Int)
    
    /**
     * Remove item from cart
     */
    @Query("DELETE FROM cart_items WHERE id = :cartItemId")
    suspend fun removeFromCart(cartItemId: Long)
    
    /**
     * Clear entire cart for session
     */
    @Query("DELETE FROM cart_items WHERE sessionId = :sessionId")
    suspend fun clearCart(sessionId: String)
    
    /**
     * Get cart item count for session
     */
    @Query("SELECT COUNT(*) FROM cart_items WHERE sessionId = :sessionId")
    suspend fun getCartItemCount(sessionId: String): Int
    
    /**
     * Get cart total for session
     */
    @Query("SELECT SUM(price * quantity) FROM cart_items WHERE sessionId = :sessionId")
    suspend fun getCartTotal(sessionId: String): Double?
    
    // Customer operations
    
    /**
     * Get all customers
     */
    @Query("SELECT * FROM customers ORDER BY name ASC")
    fun getAllCustomers(): Flow<List<Customer>>
    
    /**
     * Search customers by name or phone
     */
    @Query("SELECT * FROM customers WHERE name LIKE '%' || :query || '%' OR phoneNumber LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchCustomers(query: String): Flow<List<Customer>>
    
    /**
     * Get customer by phone number
     */
    @Query("SELECT * FROM customers WHERE phoneNumber = :phoneNumber LIMIT 1")
    suspend fun getCustomerByPhone(phoneNumber: String): Customer?
    
    /**
     * Insert new customer
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCustomer(customer: Customer): Long
    
    /**
     * Update customer
     */
    @Update
    suspend fun updateCustomer(customer: Customer)
    
    // Order operations
    
    /**
     * Get all orders
     */
    @Query("SELECT * FROM orders ORDER BY orderDate DESC")
    fun getAllOrders(): Flow<List<Order>>
    
    /**
     * Get orders for today
     */
    @Query("SELECT * FROM orders WHERE orderDate >= :startOfDay AND orderDate < :endOfDay ORDER BY orderDate DESC")
    fun getTodayOrders(startOfDay: Long, endOfDay: Long): Flow<List<Order>>
    
    /**
     * Get order by ID
     */
    @Query("SELECT * FROM orders WHERE id = :orderId")
    suspend fun getOrderById(orderId: Long): Order?
    
    /**
     * Insert new order
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrder(order: Order): Long
    
    /**
     * Update order status
     */
    @Query("UPDATE orders SET paymentStatus = :status WHERE id = :orderId")
    suspend fun updateOrderStatus(orderId: Long, status: OrderStatus)
    
    // Order Items operations
    
    /**
     * Get order items for an order
     */
    @Query("SELECT * FROM order_items WHERE orderId = :orderId ORDER BY id ASC")
    suspend fun getOrderItems(orderId: Long): List<OrderItem>
    
    /**
     * Insert order items
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrderItems(orderItems: List<OrderItem>): List<Long>
    
    // Analytics queries as mentioned in overview.txt
    
    /**
     * Get daily sales total
     */
    @Query("SELECT SUM(total) FROM orders WHERE orderDate >= :startOfDay AND orderDate < :endOfDay AND paymentStatus = 'COMPLETED'")
    suspend fun getDailySalesTotal(startOfDay: Long, endOfDay: Long): Double?
    
    /**
     * Get top selling products
     */
    @Query("""
        SELECT oi.productName, SUM(oi.quantity) as totalSold, SUM(oi.total) as totalRevenue
        FROM order_items oi
        INNER JOIN orders o ON oi.orderId = o.id
        WHERE o.orderDate >= :startDate AND o.orderDate <= :endDate
        AND o.paymentStatus = 'COMPLETED'
        GROUP BY oi.productId, oi.productName
        ORDER BY totalSold DESC
        LIMIT :limit
    """)
    suspend fun getTopSellingProducts(startDate: Long, endDate: Long, limit: Int = 10): List<TopSellingProductData>
    
    /**
     * Get sales by category
     */
    @Query("""
        SELECT p.category, SUM(oi.total) as totalRevenue, SUM(oi.quantity) as totalQuantity
        FROM order_items oi
        INNER JOIN orders o ON oi.orderId = o.id
        INNER JOIN products p ON oi.productId = p.id
        WHERE o.orderDate >= :startDate AND o.orderDate <= :endDate
        AND o.paymentStatus = 'COMPLETED'
        GROUP BY p.category
        ORDER BY totalRevenue DESC
    """)
    suspend fun getSalesByCategory(startDate: Long, endDate: Long): List<CategorySalesData>
}

/**
 * Data class for top selling products analytics
 */
data class TopSellingProductData(
    val productName: String,
    val totalSold: Int,
    val totalRevenue: Double
)

/**
 * Data class for category sales analytics
 */
data class CategorySalesData(
    val category: ProductCategory,
    val totalRevenue: Double,
    val totalQuantity: Int
)
