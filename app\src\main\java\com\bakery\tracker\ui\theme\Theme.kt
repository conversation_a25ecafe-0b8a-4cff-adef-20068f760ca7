package com.bakery.tracker.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

/**
 * Light color scheme for the Bakery Tracker app
 */
private val LightColorScheme = lightColorScheme(
    primary = BakeryPrimary,
    onPrimary = BakeryOnPrimary,
    primaryContainer = BakerySecondary,
    onPrimaryContainer = BakeryOnSecondary,
    secondary = BakerySecondary,
    onSecondary = BakeryOnSecondary,
    secondaryContainer = BakerySecondaryVariant,
    onSecondaryContainer = BakeryOnSecondary,
    tertiary = LowStockWarning,
    onTertiary = BakeryOnPrimary,
    tertiaryContainer = ProfitGreen,
    onTertiaryContainer = BakeryOnPrimary,
    error = BakeryError,
    onError = BakeryOnError,
    errorContainer = LossRed,
    onErrorContainer = BakeryOnError,
    background = BakeryBackground,
    onBackground = BakeryOnBackground,
    surface = BakerySurface,
    onSurface = BakeryOnSurface,
    surfaceVariant = BakeryBackground,
    onSurfaceVariant = BakeryOnBackground,
    outline = BakeryPrimaryVariant,
    outlineVariant = BakerySecondaryVariant,
    scrim = BakeryOnBackground,
    inverseSurface = BakeryOnSurface,
    inverseOnSurface = BakerySurface,
    inversePrimary = BakeryPrimaryVariant,
    surfaceDim = BakeryBackground,
    surfaceBright = BakerySurface,
    surfaceContainerLowest = BakerySurface,
    surfaceContainerLow = BakeryBackground,
    surfaceContainer = BakeryBackground,
    surfaceContainerHigh = BakeryBackground,
    surfaceContainerHighest = BakeryBackground
)

/**
 * Dark color scheme for the Bakery Tracker app
 */
private val DarkColorScheme = darkColorScheme(
    primary = BakeryPrimaryDark,
    onPrimary = BakeryOnPrimaryDark,
    primaryContainer = BakerySecondaryDark,
    onPrimaryContainer = BakeryOnSecondaryDark,
    secondary = BakerySecondaryDark,
    onSecondary = BakeryOnSecondaryDark,
    secondaryContainer = BakerySecondaryVariantDark,
    onSecondaryContainer = BakeryOnSecondaryDark,
    tertiary = LowStockWarningDark,
    onTertiary = BakeryOnPrimaryDark,
    tertiaryContainer = ProfitGreenDark,
    onTertiaryContainer = BakeryOnPrimaryDark,
    error = BakeryErrorDark,
    onError = BakeryOnErrorDark,
    errorContainer = LossRedDark,
    onErrorContainer = BakeryOnErrorDark,
    background = BakeryBackgroundDark,
    onBackground = BakeryOnBackgroundDark,
    surface = BakerySurfaceDark,
    onSurface = BakeryOnSurfaceDark,
    surfaceVariant = BakeryBackgroundDark,
    onSurfaceVariant = BakeryOnBackgroundDark,
    outline = BakeryPrimaryVariantDark,
    outlineVariant = BakerySecondaryVariantDark,
    scrim = BakeryOnBackgroundDark,
    inverseSurface = BakeryOnSurfaceDark,
    inverseOnSurface = BakerySurfaceDark,
    inversePrimary = BakeryPrimaryVariantDark,
    surfaceDim = BakeryBackgroundDark,
    surfaceBright = BakerySurfaceDark,
    surfaceContainerLowest = BakerySurfaceDark,
    surfaceContainerLow = BakeryBackgroundDark,
    surfaceContainer = BakeryBackgroundDark,
    surfaceContainerHigh = BakeryBackgroundDark,
    surfaceContainerHighest = BakeryBackgroundDark
)

/**
 * Main theme composable for the Bakery Tracker app
 * 
 * @param darkTheme Whether to use dark theme
 * @param dynamicColor Whether to use dynamic color (Android 12+)
 * @param content The content to be themed
 */
@Composable
fun BakeryTrackerTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
