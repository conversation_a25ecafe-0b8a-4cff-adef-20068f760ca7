package com.bakery.tracker.data.local

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.bakery.tracker.data.local.dao.*
import com.bakery.tracker.data.models.*

/**
 * Room database for the Bakery Tracker application
 */
@Database(
    entities = [
        Ingredient::class,
        Product::class,
        Recipe::class,
        RecipeIngredient::class,
        ProductionLog::class,
        Bill::class,
        BillItem::class,
        CartItem::class,
        Customer::class,
        Order::class,
        OrderItem::class
    ],
    version = 2,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class BakeryDatabase : RoomDatabase() {
    
    abstract fun ingredientDao(): IngredientDao
    abstract fun productDao(): ProductDao
    abstract fun recipeDao(): RecipeDao
    abstract fun productionDao(): ProductionDao
    abstract fun billDao(): BillDao
    abstract fun cartDao(): CartDao
    
    companion object {
        @Volatile
        private var INSTANCE: BakeryDatabase? = null
        
        fun getDatabase(context: Context): BakeryDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    BakeryDatabase::class.java,
                    "bakery_database"
                )
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}

/**
 * Type converters for Room database
 */
class Converters {

    @androidx.room.TypeConverter
    fun fromPaymentStatus(status: PaymentStatus): String {
        return status.name
    }

    @androidx.room.TypeConverter
    fun toPaymentStatus(status: String): PaymentStatus {
        return PaymentStatus.valueOf(status)
    }

    @androidx.room.TypeConverter
    fun fromProductCategory(category: ProductCategory): String {
        return category.name
    }

    @androidx.room.TypeConverter
    fun toProductCategory(category: String): ProductCategory {
        return ProductCategory.valueOf(category)
    }

    @androidx.room.TypeConverter
    fun fromIngredientUnit(unit: IngredientUnit): String {
        return unit.name
    }

    @androidx.room.TypeConverter
    fun toIngredientUnit(unit: String): IngredientUnit {
        return IngredientUnit.valueOf(unit)
    }

    @androidx.room.TypeConverter
    fun fromRecipeDifficulty(difficulty: RecipeDifficulty): String {
        return difficulty.name
    }

    @androidx.room.TypeConverter
    fun toRecipeDifficulty(difficulty: String): RecipeDifficulty {
        return RecipeDifficulty.valueOf(difficulty)
    }

    @androidx.room.TypeConverter
    fun fromPaymentMethod(method: PaymentMethod): String {
        return method.name
    }

    @androidx.room.TypeConverter
    fun toPaymentMethod(method: String): PaymentMethod {
        return PaymentMethod.valueOf(method)
    }

    @androidx.room.TypeConverter
    fun fromOrderStatus(status: OrderStatus): String {
        return status.name
    }

    @androidx.room.TypeConverter
    fun toOrderStatus(status: String): OrderStatus {
        return OrderStatus.valueOf(status)
    }
}
