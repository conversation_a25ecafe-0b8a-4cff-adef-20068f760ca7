package com.bakery.tracker.data.local.dao

import androidx.room.*
import com.bakery.tracker.data.models.Ingredient
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for Ingredient operations
 */
@Dao
interface IngredientDao {
    
    /**
     * Get all ingredients as a Flow for reactive updates
     */
    @Query("SELECT * FROM ingredients ORDER BY name ASC")
    fun getAllIngredients(): Flow<List<Ingredient>>
    
    /**
     * Get all active ingredients (not deleted)
     */
    @Query("SELECT * FROM ingredients WHERE currentStock >= 0 ORDER BY name ASC")
    fun getActiveIngredients(): Flow<List<Ingredient>>
    
    /**
     * Get ingredients with low stock
     */
    @Query("SELECT * FROM ingredients WHERE currentStock <= lowStockThreshold ORDER BY name ASC")
    fun getLowStockIngredients(): Flow<List<Ingredient>>
    
    /**
     * Get ingredient by ID
     */
    @Query("SELECT * FROM ingredients WHERE id = :id")
    suspend fun getIngredientById(id: Long): Ingredient?
    
    /**
     * Get ingredient by name
     */
    @Query("SELECT * FROM ingredients WHERE name = :name LIMIT 1")
    suspend fun getIngredientByName(name: String): Ingredient?
    
    /**
     * Search ingredients by name
     */
    @Query("SELECT * FROM ingredients WHERE name LIKE '%' || :searchQuery || '%' ORDER BY name ASC")
    fun searchIngredients(searchQuery: String): Flow<List<Ingredient>>
    
    /**
     * Insert a new ingredient
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertIngredient(ingredient: Ingredient): Long
    
    /**
     * Insert multiple ingredients
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertIngredients(ingredients: List<Ingredient>): List<Long>
    
    /**
     * Update an existing ingredient
     */
    @Update
    suspend fun updateIngredient(ingredient: Ingredient)
    
    /**
     * Update ingredient stock
     */
    @Query("UPDATE ingredients SET currentStock = :newStock, updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateIngredientStock(id: Long, newStock: Double, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Update ingredient price
     */
    @Query("UPDATE ingredients SET lastPaidPrice = :newPrice, updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateIngredientPrice(id: Long, newPrice: Double, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Decrease ingredient stock (for production)
     */
    @Query("UPDATE ingredients SET currentStock = currentStock - :amount, updatedAt = :updatedAt WHERE id = :id AND currentStock >= :amount")
    suspend fun decreaseStock(id: Long, amount: Double, updatedAt: Long = System.currentTimeMillis()): Int
    
    /**
     * Increase ingredient stock (for restocking)
     */
    @Query("UPDATE ingredients SET currentStock = currentStock + :amount, updatedAt = :updatedAt WHERE id = :id")
    suspend fun increaseStock(id: Long, amount: Double, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Delete an ingredient
     */
    @Delete
    suspend fun deleteIngredient(ingredient: Ingredient)
    
    /**
     * Delete ingredient by ID
     */
    @Query("DELETE FROM ingredients WHERE id = :id")
    suspend fun deleteIngredientById(id: Long)
    
    /**
     * Get total count of ingredients
     */
    @Query("SELECT COUNT(*) FROM ingredients")
    suspend fun getIngredientCount(): Int
    
    /**
     * Get total value of all ingredients in stock
     */
    @Query("SELECT SUM(currentStock * lastPaidPrice) FROM ingredients")
    suspend fun getTotalStockValue(): Double?
}
