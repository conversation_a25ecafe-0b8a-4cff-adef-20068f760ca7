@echo off
setlocal enabledelayedexpansion
echo ========================================
echo Fix Build Until Success
echo ========================================
echo.

:attempt1
echo [ATTEMPT 1] Removing corrupted Gradle cache completely...
call gradlew --stop 2>nul
rmdir /s /q "%USERPROFILE%\.gradle" 2>nul
rmdir /s /q ".gradle" 2>nul

echo [ATTEMPT 1] Regenerating Gradle wrapper...
echo distributionBase=GRADLE_USER_HOME > gradle\wrapper\gradle-wrapper.properties
echo distributionPath=wrapper/dists >> gradle\wrapper\gradle-wrapper.properties
echo distributionUrl=https\://services.gradle.org/distributions/gradle-8.7-bin.zip >> gradle\wrapper\gradle-wrapper.properties
echo zipStoreBase=GRADLE_USER_HOME >> gradle\wrapper\gradle-wrapper.properties
echo zipStorePath=wrapper/dists >> gradle\wrapper\gradle-wrapper.properties

echo [ATTEMPT 1] Downloading fresh Gradle wrapper...
powershell -Command "Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.7.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle/wrapper/gradle-wrapper.jar'" 2>nul

echo [ATTEMPT 1] Building project...
call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata --refresh-dependencies
if %ERRORLEVEL% EQU 0 goto success

:attempt2
echo.
echo [ATTEMPT 2] Trying with different Gradle version...
echo distributionUrl=https\://services.gradle.org/distributions/gradle-8.5-bin.zip > gradle\wrapper\gradle-wrapper.properties
echo distributionBase=GRADLE_USER_HOME >> gradle\wrapper\gradle-wrapper.properties
echo distributionPath=wrapper/dists >> gradle\wrapper\gradle-wrapper.properties
echo zipStoreBase=GRADLE_USER_HOME >> gradle\wrapper\gradle-wrapper.properties
echo zipStorePath=wrapper/dists >> gradle\wrapper\gradle-wrapper.properties

call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata
if %ERRORLEVEL% EQU 0 goto success

:attempt3
echo.
echo [ATTEMPT 3] Trying with minimal build options...
call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata -x lint -x lintVitalRelease --offline
if %ERRORLEVEL% EQU 0 goto success

:attempt4
echo.
echo [ATTEMPT 4] Trying online build with fresh dependencies...
call gradlew clean --no-daemon
call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata --refresh-dependencies
if %ERRORLEVEL% EQU 0 goto success

:attempt5
echo.
echo [ATTEMPT 5] Trying with Gradle 7.6...
echo distributionUrl=https\://services.gradle.org/distributions/gradle-7.6-bin.zip > gradle\wrapper\gradle-wrapper.properties
echo distributionBase=GRADLE_USER_HOME >> gradle\wrapper\gradle-wrapper.properties
echo distributionPath=wrapper/dists >> gradle\wrapper\gradle-wrapper.properties
echo zipStoreBase=GRADLE_USER_HOME >> gradle\wrapper\gradle-wrapper.properties
echo zipStorePath=wrapper/dists >> gradle\wrapper\gradle-wrapper.properties

call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata
if %ERRORLEVEL% EQU 0 goto success

:attempt6
echo.
echo [ATTEMPT 6] Trying with daemon enabled...
call gradlew assembleDebug -x checkDebugAarMetadata
if %ERRORLEVEL% EQU 0 goto success

:attempt7
echo.
echo [ATTEMPT 7] Trying debug build only...
call gradlew app:assembleDebug --no-daemon -x checkDebugAarMetadata
if %ERRORLEVEL% EQU 0 goto success

:attempt8
echo.
echo [ATTEMPT 8] Trying with parallel disabled...
call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata --no-parallel
if %ERRORLEVEL% EQU 0 goto success

:attempt9
echo.
echo [ATTEMPT 9] Trying with configuration cache disabled...
call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata --no-configuration-cache
if %ERRORLEVEL% EQU 0 goto success

:attempt10
echo.
echo [ATTEMPT 10] Final attempt with all optimizations disabled...
call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata -x lint -x lintVitalRelease --no-parallel --no-configuration-cache --refresh-dependencies
if %ERRORLEVEL% EQU 0 goto success

echo.
echo ========================================
echo ALL ATTEMPTS FAILED
echo ========================================
echo.
echo Manual intervention required:
echo 1. Restart your computer
echo 2. Open Android Studio
echo 3. Open project and let it sync
echo 4. Build from Android Studio
echo.
goto end

:success
echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.
echo APK Location: app\build\outputs\apk\debug\app-debug.apk
echo.
echo Your Android app has been successfully built!
echo You can now install it using:
echo adb install app\build\outputs\apk\debug\app-debug.apk
echo.

:end
pause
