package com.bakery.tracker.data.local.dao

import androidx.room.*
import com.bakery.tracker.data.models.Recipe
import com.bakery.tracker.data.models.RecipeIngredient
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for Recipe operations
 */
@Dao
interface RecipeDao {
    
    /**
     * Get all recipes as a Flow for reactive updates
     */
    @Query("SELECT * FROM recipes ORDER BY name ASC")
    fun getAllRecipes(): Flow<List<Recipe>>
    
    /**
     * Get all active recipes
     */
    @Query("SELECT * FROM recipes WHERE isActive = 1 ORDER BY name ASC")
    fun getActiveRecipes(): Flow<List<Recipe>>
    
    /**
     * Get recipes for a specific product
     */
    @Query("SELECT * FROM recipes WHERE productId = :productId AND isActive = 1 ORDER BY name ASC")
    fun getRecipesForProduct(productId: Long): Flow<List<Recipe>>
    
    /**
     * Get recipe by ID
     */
    @Query("SELECT * FROM recipes WHERE id = :id")
    suspend fun getRecipeById(id: Long): Recipe?
    
    /**
     * Get recipe by name
     */
    @Query("SELECT * FROM recipes WHERE name = :name LIMIT 1")
    suspend fun getRecipeByName(name: String): Recipe?
    
    /**
     * Search recipes by name
     */
    @Query("SELECT * FROM recipes WHERE name LIKE '%' || :searchQuery || '%' AND isActive = 1 ORDER BY name ASC")
    fun searchRecipes(searchQuery: String): Flow<List<Recipe>>
    
    /**
     * Insert a new recipe
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecipe(recipe: Recipe): Long
    
    /**
     * Update an existing recipe
     */
    @Update
    suspend fun updateRecipe(recipe: Recipe)
    
    /**
     * Toggle recipe active status
     */
    @Query("UPDATE recipes SET isActive = :isActive, updatedAt = :updatedAt WHERE id = :id")
    suspend fun toggleRecipeActiveStatus(id: Long, isActive: Boolean, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Delete a recipe
     */
    @Delete
    suspend fun deleteRecipe(recipe: Recipe)
    
    /**
     * Delete recipe by ID
     */
    @Query("DELETE FROM recipes WHERE id = :id")
    suspend fun deleteRecipeById(id: Long)
    
    // Recipe Ingredients operations
    
    /**
     * Get all ingredients for a recipe
     */
    @Query("SELECT * FROM recipe_ingredients WHERE recipeId = :recipeId")
    suspend fun getRecipeIngredients(recipeId: Long): List<RecipeIngredient>
    
    /**
     * Get recipe ingredients with ingredient details
     */
    @Query("""
        SELECT ri.*, i.name as ingredientName, i.unit as ingredientUnit, i.currentStock, i.lastPaidPrice
        FROM recipe_ingredients ri
        INNER JOIN ingredients i ON ri.ingredientId = i.id
        WHERE ri.recipeId = :recipeId
        ORDER BY i.name ASC
    """)
    fun getRecipeIngredientsWithDetails(recipeId: Long): Flow<List<RecipeIngredientWithDetails>>
    
    /**
     * Insert recipe ingredient
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecipeIngredient(recipeIngredient: RecipeIngredient): Long
    
    /**
     * Insert multiple recipe ingredients
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecipeIngredients(recipeIngredients: List<RecipeIngredient>): List<Long>
    
    /**
     * Update recipe ingredient
     */
    @Update
    suspend fun updateRecipeIngredient(recipeIngredient: RecipeIngredient)
    
    /**
     * Delete recipe ingredient
     */
    @Delete
    suspend fun deleteRecipeIngredient(recipeIngredient: RecipeIngredient)
    
    /**
     * Delete all ingredients for a recipe
     */
    @Query("DELETE FROM recipe_ingredients WHERE recipeId = :recipeId")
    suspend fun deleteAllRecipeIngredients(recipeId: Long)
    
    /**
     * Get total count of recipes
     */
    @Query("SELECT COUNT(*) FROM recipes WHERE isActive = 1")
    suspend fun getActiveRecipeCount(): Int
}

/**
 * Data class for recipe ingredient with ingredient details
 */
data class RecipeIngredientWithDetails(
    val id: Long,
    val recipeId: Long,
    val ingredientId: Long,
    val quantity: Double,
    val unit: String,
    val notes: String,
    val ingredientName: String,
    val ingredientUnit: String,
    val currentStock: Double,
    val lastPaidPrice: Double
)
