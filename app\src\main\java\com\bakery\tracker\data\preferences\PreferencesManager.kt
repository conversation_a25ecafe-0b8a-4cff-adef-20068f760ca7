package com.bakery.tracker.data.preferences

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager for handling app preferences using DataStore
 */
@Singleton
class PreferencesManager @Inject constructor(
    private val dataStore: DataStore<Preferences>
) {
    
    companion object {
        private val DARK_THEME_KEY = booleanPreferencesKey("dark_theme")
        private val USER_ROLE_KEY = stringPreferencesKey("user_role")
        private val USER_TOKEN_KEY = stringPreferencesKey("user_token")
        private val USER_NAME_KEY = stringPreferencesKey("user_name")
        private val BACKUP_ENABLED_KEY = booleanPreferencesKey("backup_enabled")
        private val LOW_STOCK_NOTIFICATIONS_KEY = booleanPreferencesKey("low_stock_notifications")
    }
    
    /**
     * Flow for dark theme preference
     */
    val isDarkTheme: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[DARK_THEME_KEY] ?: false
    }
    
    /**
     * Flow for user role
     */
    val userRole: Flow<String> = dataStore.data.map { preferences ->
        preferences[USER_ROLE_KEY] ?: "staff"
    }
    
    /**
     * Flow for user token (mock JWT)
     */
    val userToken: Flow<String?> = dataStore.data.map { preferences ->
        preferences[USER_TOKEN_KEY]
    }
    
    /**
     * Flow for user name
     */
    val userName: Flow<String> = dataStore.data.map { preferences ->
        preferences[USER_NAME_KEY] ?: ""
    }
    
    /**
     * Flow for backup enabled preference
     */
    val isBackupEnabled: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[BACKUP_ENABLED_KEY] ?: true
    }
    
    /**
     * Flow for low stock notifications preference
     */
    val isLowStockNotificationsEnabled: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[LOW_STOCK_NOTIFICATIONS_KEY] ?: true
    }
    
    /**
     * Toggle dark theme preference
     */
    suspend fun toggleDarkTheme() {
        dataStore.edit { preferences ->
            val currentValue = preferences[DARK_THEME_KEY] ?: false
            preferences[DARK_THEME_KEY] = !currentValue
        }
    }
    
    /**
     * Set dark theme preference
     */
    suspend fun setDarkTheme(isDark: Boolean) {
        dataStore.edit { preferences ->
            preferences[DARK_THEME_KEY] = isDark
        }
    }
    
    /**
     * Set user role
     */
    suspend fun setUserRole(role: String) {
        dataStore.edit { preferences ->
            preferences[USER_ROLE_KEY] = role
        }
    }
    
    /**
     * Set user token (mock JWT)
     */
    suspend fun setUserToken(token: String?) {
        dataStore.edit { preferences ->
            if (token != null) {
                preferences[USER_TOKEN_KEY] = token
            } else {
                preferences.remove(USER_TOKEN_KEY)
            }
        }
    }
    
    /**
     * Set user name
     */
    suspend fun setUserName(name: String) {
        dataStore.edit { preferences ->
            preferences[USER_NAME_KEY] = name
        }
    }
    
    /**
     * Set backup enabled preference
     */
    suspend fun setBackupEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[BACKUP_ENABLED_KEY] = enabled
        }
    }
    
    /**
     * Set low stock notifications preference
     */
    suspend fun setLowStockNotificationsEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[LOW_STOCK_NOTIFICATIONS_KEY] = enabled
        }
    }
    
    /**
     * Clear all user data (logout)
     */
    suspend fun clearUserData() {
        dataStore.edit { preferences ->
            preferences.remove(USER_TOKEN_KEY)
            preferences.remove(USER_NAME_KEY)
            preferences[USER_ROLE_KEY] = "staff"
        }
    }
    
    /**
     * Check if user is logged in
     */
    suspend fun isLoggedIn(): Boolean {
        return dataStore.data.map { preferences ->
            preferences[USER_TOKEN_KEY] != null
        }.let { flow ->
            // In a real implementation, you'd collect this flow
            // For now, return false as placeholder
            false
        }
    }
    
    /**
     * Check if user is admin
     */
    suspend fun isAdmin(): Boolean {
        return dataStore.data.map { preferences ->
            preferences[USER_ROLE_KEY] == "admin"
        }.let { flow ->
            // In a real implementation, you'd collect this flow
            // For now, return false as placeholder
            false
        }
    }
}
