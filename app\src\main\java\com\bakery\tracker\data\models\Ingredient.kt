package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Represents an ingredient used in bakery production.
 * Units: kg, grams, liters, pieces, etc. (as per overview.txt)
 *
 * @param id Unique identifier for the ingredient
 * @param name Name of the ingredient (e.g., "Flour", "Sugar")
 * @param unit Unit of measurement (kg, grams, liters, pieces, etc.)
 * @param currentStock Current quantity in stock
 * @param minimumThreshold Minimum quantity before low stock alert
 * @param pricePerUnit Price per unit (₹)
 * @param category Ingredient category for organization
 * @param supplier Supplier name or information
 * @param priceHistory JSON string storing price history
 * @param createdAt Timestamp when ingredient was added
 * @param updatedAt Timestamp when ingredient was last updated
 */
@Entity(tableName = "ingredients")
data class Ingredient(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val unit: IngredientUnit,
    val currentStock: Double,
    val minimumThreshold: Double,
    val pricePerUnit: Double,
    val category: String = "",
    val supplier: String = "",
    val priceHistory: String = "[]", // JSON array of price history
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * Checks if the ingredient is below the minimum threshold (low stock detection)
     */
    fun isLowStock(): Boolean = currentStock < minimumThreshold

    /**
     * Calculates the total value of current stock (inventory value calculation)
     */
    fun getTotalStockValue(): Double = currentStock * pricePerUnit

    /**
     * Gets the unit display name
     */
    fun getUnitDisplayName(): String = unit.displayName
}

/**
 * Ingredient units as mentioned in overview.txt
 */
enum class IngredientUnit(val displayName: String) {
    KG("kg"),
    GRAMS("grams"),
    LITERS("liters"),
    ML("ml"),
    PIECES("pieces"),
    CUPS("cups"),
    TBSP("tbsp"),
    TSP("tsp")
}
