package com.bakery.tracker.data.repository

import com.bakery.tracker.data.local.dao.CartDao
import com.bakery.tracker.data.models.*
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing cart and order operations
 * Implements POS system functionality as described in overview.txt
 */
@Singleton
class CartRepository @Inject constructor(
    private val cartDao: CartDao
) {
    
    // Cart operations
    
    /**
     * Get cart items for session
     */
    fun getCartItems(sessionId: String): Flow<List<CartItem>> = cartDao.getCartItems(sessionId)
    
    /**
     * Add item to cart
     */
    suspend fun addToCart(cartItem: CartItem): Long = cartDao.addToCart(cartItem)
    
    /**
     * Update cart item quantity
     */
    suspend fun updateCartItemQuantity(cartItemId: Long, quantity: Int) = 
        cartDao.updateCartItemQuantity(cartItemId, quantity)
    
    /**
     * Remove item from cart
     */
    suspend fun removeFromCart(cartItemId: Long) = cartDao.removeFromCart(cartItemId)
    
    /**
     * Clear entire cart
     */
    suspend fun clearCart(sessionId: String) = cartDao.clearCart(sessionId)
    
    /**
     * Get cart total
     */
    suspend fun getCartTotal(sessionId: String): Double = cartDao.getCartTotal(sessionId) ?: 0.0
    
    // Customer operations
    
    /**
     * Get all customers
     */
    fun getAllCustomers(): Flow<List<Customer>> = cartDao.getAllCustomers()
    
    /**
     * Search customers
     */
    fun searchCustomers(query: String): Flow<List<Customer>> = cartDao.searchCustomers(query)
    
    /**
     * Get customer by phone
     */
    suspend fun getCustomerByPhone(phoneNumber: String): Customer? = 
        cartDao.getCustomerByPhone(phoneNumber)
    
    /**
     * Add new customer
     */
    suspend fun addCustomer(customer: Customer): Long = cartDao.insertCustomer(customer)
    
    /**
     * Update customer
     */
    suspend fun updateCustomer(customer: Customer) = cartDao.updateCustomer(customer)
    
    // Order operations
    
    /**
     * Get all orders
     */
    fun getAllOrders(): Flow<List<Order>> = cartDao.getAllOrders()
    
    /**
     * Get today's orders
     */
    fun getTodayOrders(startOfDay: Long, endOfDay: Long): Flow<List<Order>> = 
        cartDao.getTodayOrders(startOfDay, endOfDay)
    
    /**
     * Get order by ID
     */
    suspend fun getOrderById(orderId: Long): Order? = cartDao.getOrderById(orderId)
    
    /**
     * Create order from cart
     * Implements the checkout process from overview.txt
     */
    suspend fun createOrderFromCart(
        sessionId: String,
        customerName: String,
        customerPhone: String,
        paymentMethod: PaymentMethod,
        discount: Double = 0.0,
        tax: Double = 0.0
    ): Long {
        val cartItems = cartDao.getCartItems(sessionId)
        val cartTotal = cartDao.getCartTotal(sessionId) ?: 0.0
        
        // Create order
        val billNumber = generateBillNumber()
        val order = Order(
            billNumber = billNumber,
            customerName = customerName,
            customerPhone = customerPhone,
            subtotal = cartTotal,
            discount = discount,
            tax = tax,
            total = cartTotal - discount + tax,
            paymentMethod = paymentMethod,
            paymentStatus = OrderStatus.COMPLETED
        )
        
        val orderId = cartDao.insertOrder(order)
        
        // Create order items from cart
        val orderItems = cartItems.first().map { cartItem ->
            OrderItem(
                orderId = orderId,
                productId = cartItem.productId,
                productName = cartItem.productName,
                price = cartItem.price,
                quantity = cartItem.quantity,
                total = cartItem.getTotalPrice()
            )
        }
        
        cartDao.insertOrderItems(orderItems)
        
        // Clear cart after successful order
        cartDao.clearCart(sessionId)
        
        return orderId
    }
    
    /**
     * Update order status
     */
    suspend fun updateOrderStatus(orderId: Long, status: OrderStatus) = 
        cartDao.updateOrderStatus(orderId, status)
    
    /**
     * Get order items
     */
    suspend fun getOrderItems(orderId: Long): List<OrderItem> = cartDao.getOrderItems(orderId)
    
    // Analytics operations as mentioned in overview.txt
    
    /**
     * Get daily sales total
     */
    suspend fun getDailySalesTotal(startOfDay: Long, endOfDay: Long): Double = 
        cartDao.getDailySalesTotal(startOfDay, endOfDay) ?: 0.0
    
    /**
     * Get top selling products
     */
    suspend fun getTopSellingProducts(startDate: Long, endDate: Long, limit: Int = 10): List<TopSellingProductData> = 
        cartDao.getTopSellingProducts(startDate, endDate, limit)
    
    /**
     * Get sales by category
     */
    suspend fun getSalesByCategory(startDate: Long, endDate: Long): List<CategorySalesData> = 
        cartDao.getSalesByCategory(startDate, endDate)
    
    /**
     * Generate unique bill number
     * Format: BT-YYYYMMDD-XXXX (BT = Bakery Tracker)
     */
    private fun generateBillNumber(): String {
        val timestamp = System.currentTimeMillis()
        val dateFormat = java.text.SimpleDateFormat("yyyyMMdd", java.util.Locale.getDefault())
        val date = dateFormat.format(java.util.Date(timestamp))
        val random = (1000..9999).random()
        return "BT-$date-$random"
    }
}
