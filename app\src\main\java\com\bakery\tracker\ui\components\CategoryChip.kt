package com.bakery.tracker.ui.components

import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.bakery.tracker.ui.theme.BakeryTrackerTheme

/**
 * Category filter chip component
 * Used for filtering products by category in POS screen
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryChip(
    category: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        onClick = onClick,
        label = { Text(category) },
        selected = isSelected,
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun CategoryChipPreview() {
    BakeryTrackerTheme {
        CategoryChip(
            category = "Cakes",
            isSelected = true,
            onClick = {}
        )
    }
}
