<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name"><PERSON><PERSON> Tracker</string>
    
    <!-- Navigation -->
    <string name="nav_dashboard">Dashboard</string>
    <string name="nav_recipes">Recipes</string>
    <string name="nav_production">Production</string>
    <string name="nav_stock">Stock</string>
    <string name="nav_pricing">Pricing</string>
    <string name="nav_bills">Bills</string>
    <string name="nav_orders">Orders</string>
    <string name="nav_staff">Staff</string>
    <string name="nav_settings">Settings</string>
    
    <!-- Login -->
    <string name="login_title">Bakery Tracker Pro</string>
    <string name="login_subtitle">Production Management System</string>
    <string name="login_username">Username</string>
    <string name="login_password">Password</string>
    <string name="login_button">Login</string>
    <string name="login_role_staff">Staff</string>
    <string name="login_role_admin">Admin</string>
    <string name="login_demo_credentials">Demo Credentials</string>
    <string name="login_demo_info">Username: demo\nPassword: demo123\nRole: Staff or Admin</string>
    
    <!-- Dashboard -->
    <string name="dashboard_title">Dashboard</string>
    <string name="dashboard_subtitle">Today\'s Overview</string>
    <string name="dashboard_refresh">Refresh</string>
    <string name="dashboard_low_stock_alerts">Low Stock Alerts</string>
    <string name="dashboard_production_chart">Today\'s Production</string>
    <string name="dashboard_revenue_chart">Weekly Revenue</string>
    <string name="dashboard_recent_activity">Recent Activity</string>
    <string name="dashboard_no_activity">No recent activity</string>
    
    <!-- Quick Stats -->
    <string name="stats_production_title">Today\'s Production</string>
    <string name="stats_production_units">units</string>
    <string name="stats_revenue_title">Revenue</string>
    <string name="stats_revenue_today">today</string>
    <string name="stats_orders_title">Orders</string>
    <string name="stats_orders_pending">pending</string>
    <string name="stats_stock_title">Stock Items</string>
    <string name="stats_stock_low">low stock</string>
    
    <!-- Low Stock -->
    <string name="low_stock_current">Current: %1$s %2$s</string>
    <string name="low_stock_threshold">Threshold: %1$s %2$s</string>
    <string name="low_stock_restock">Restock</string>
    
    <!-- Recipes -->
    <string name="recipes_title">Recipes</string>
    <string name="recipes_subtitle">Manage your bakery recipes</string>
    <string name="recipes_add">Add Recipe</string>
    <string name="recipes_coming_soon">Recipe Management</string>
    <string name="recipes_description">Create and manage recipes with ingredient lists, batch sizes, and cooking instructions.</string>
    
    <!-- Production -->
    <string name="production_title">Production</string>
    <string name="production_subtitle">Track daily production</string>
    <string name="production_record">Record Production</string>
    <string name="production_coming_soon">Production Tracking</string>
    <string name="production_description">Record production quantities, track ingredient usage, and manage batch information with undo functionality.</string>
    
    <!-- Stock -->
    <string name="stock_title">Stock</string>
    <string name="stock_subtitle">Manage ingredient inventory</string>
    <string name="stock_add">Add Ingredient</string>
    <string name="stock_coming_soon">Stock Management</string>
    <string name="stock_description">Track ingredient quantities, set low-stock thresholds, manage prices, and import/export CSV data.</string>
    
    <!-- Pricing -->
    <string name="pricing_title">Pricing</string>
    <string name="pricing_subtitle">Manage product pricing</string>
    <string name="pricing_coming_soon">Pricing Management</string>
    <string name="pricing_description">Calculate real-time unit costs based on ingredient prices with manual override options.</string>
    
    <!-- Bill Capture -->
    <string name="bill_capture_title">Bill Capture</string>
    <string name="bill_capture_subtitle">Capture and manage bills</string>
    <string name="bill_capture_camera">Capture Bill</string>
    <string name="bill_capture_coming_soon">Bill Capture</string>
    <string name="bill_capture_description">Use CameraX to capture bills, preview images, and map ingredients with undo functionality.</string>
    
    <!-- Orders -->
    <string name="orders_title">Orders</string>
    <string name="orders_subtitle">Manage customer orders</string>
    <string name="orders_new">New Order</string>
    <string name="orders_coming_soon">Order Management</string>
    <string name="orders_description">Create customer orders, generate PDF invoices with QR codes, and export billing data to CSV.</string>
    
    <!-- Staff -->
    <string name="staff_title">Staff</string>
    <string name="staff_subtitle">Manage staff and permissions</string>
    <string name="staff_coming_soon">Staff Management</string>
    <string name="staff_description">Manage staff roles, permissions, and access control for different features.</string>
    
    <!-- Settings -->
    <string name="settings_title">Settings</string>
    <string name="settings_subtitle">App preferences and configuration</string>
    <string name="settings_user_info">User Information</string>
    <string name="settings_appearance">Appearance</string>
    <string name="settings_demo_features">Demo Features</string>
    <string name="settings_account">Account</string>
    <string name="settings_dark_mode">Dark Mode</string>
    <string name="settings_dark_mode_enabled">Enabled</string>
    <string name="settings_dark_mode_disabled">Disabled</string>
    <string name="settings_toggle_role">Toggle User Role</string>
    <string name="settings_toggle_role_desc">Switch between Admin and Staff</string>
    <string name="settings_switch_to_staff">Switch to Staff</string>
    <string name="settings_switch_to_admin">Switch to Admin</string>
    <string name="settings_logout">Logout</string>
    <string name="settings_role_format">Role: %1$s</string>
    
    <!-- Common -->
    <string name="coming_soon">Coming Soon</string>
    <string name="no_data">No data available</string>
    <string name="loading">Loading...</string>
    <string name="error">Error</string>
    <string name="retry">Retry</string>
    <string name="cancel">Cancel</string>
    <string name="save">Save</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>
    <string name="add">Add</string>
    <string name="search">Search</string>
    <string name="filter">Filter</string>
    <string name="sort">Sort</string>
    
    <!-- Content Descriptions -->
    <string name="cd_app_logo">App Logo</string>
    <string name="cd_toggle_password">Toggle password visibility</string>
    <string name="cd_refresh">Refresh</string>
    <string name="cd_warning">Warning</string>
    <string name="cd_user">User</string>
    <string name="cd_theme">Theme</string>
    <string name="cd_logout">Logout</string>
</resources>
