package com.bakery.tracker.data.local.dao

import androidx.room.*
import com.bakery.tracker.data.models.Product
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for Product operations
 */
@Dao
interface ProductDao {
    
    /**
     * Get all products as a Flow for reactive updates
     */
    @Query("SELECT * FROM products ORDER BY name ASC")
    fun getAllProducts(): Flow<List<Product>>
    
    /**
     * Get all active products
     */
    @Query("SELECT * FROM products WHERE isActive = 1 ORDER BY name ASC")
    fun getActiveProducts(): Flow<List<Product>>
    
    /**
     * Get products by category
     */
    @Query("SELECT * FROM products WHERE category = :category AND isActive = 1 ORDER BY name ASC")
    fun getProductsByCategory(category: String): Flow<List<Product>>
    
    /**
     * Get all categories
     */
    @Query("SELECT DISTINCT category FROM products WHERE category != '' ORDER BY category ASC")
    fun getAllCategories(): Flow<List<String>>
    
    /**
     * Get product by ID
     */
    @Query("SELECT * FROM products WHERE id = :id")
    suspend fun getProductById(id: Long): Product?
    
    /**
     * Get product by name
     */
    @Query("SELECT * FROM products WHERE name = :name LIMIT 1")
    suspend fun getProductByName(name: String): Product?
    
    /**
     * Search products by name
     */
    @Query("SELECT * FROM products WHERE name LIKE '%' || :searchQuery || '%' AND isActive = 1 ORDER BY name ASC")
    fun searchProducts(searchQuery: String): Flow<List<Product>>
    
    /**
     * Insert a new product
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: Product): Long
    
    /**
     * Insert multiple products
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProducts(products: List<Product>): List<Long>
    
    /**
     * Update an existing product
     */
    @Update
    suspend fun updateProduct(product: Product)
    
    /**
     * Update product cost price
     */
    @Query("UPDATE products SET costPrice = :newCostPrice, updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateProductCostPrice(id: Long, newCostPrice: Double, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Update product selling price
     */
    @Query("UPDATE products SET sellingPrice = :newSellingPrice, updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateProductSellingPrice(id: Long, newSellingPrice: Double, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Set manual cost override
     */
    @Query("UPDATE products SET manualCostOverride = :manualCost, updatedAt = :updatedAt WHERE id = :id")
    suspend fun setManualCostOverride(id: Long, manualCost: Double?, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Toggle product active status
     */
    @Query("UPDATE products SET isActive = :isActive, updatedAt = :updatedAt WHERE id = :id")
    suspend fun toggleProductActiveStatus(id: Long, isActive: Boolean, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Delete a product
     */
    @Delete
    suspend fun deleteProduct(product: Product)
    
    /**
     * Delete product by ID
     */
    @Query("DELETE FROM products WHERE id = :id")
    suspend fun deleteProductById(id: Long)
    
    /**
     * Get total count of products
     */
    @Query("SELECT COUNT(*) FROM products WHERE isActive = 1")
    suspend fun getActiveProductCount(): Int
    
    /**
     * Get products with highest profit margin
     */
    @Query("SELECT * FROM products WHERE isActive = 1 ORDER BY (sellingPrice - COALESCE(manualCostOverride, costPrice)) DESC LIMIT :limit")
    suspend fun getTopProfitableProducts(limit: Int = 10): List<Product>
}
