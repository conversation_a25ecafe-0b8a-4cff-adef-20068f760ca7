package com.bakery.tracker.data.repository

import com.bakery.tracker.data.local.dao.ProductDao
import com.bakery.tracker.data.models.Product
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing product data operations
 */
@Singleton
class ProductRepository @Inject constructor(
    private val productDao: ProductDao
) {
    
    /**
     * Get all products as a Flow
     */
    fun getAllProducts(): Flow<List<Product>> = productDao.getAllProducts()
    
    /**
     * Get all active products
     */
    fun getActiveProducts(): Flow<List<Product>> = productDao.getActiveProducts()

    /**
     * Initialize sample data for demo (as per overview.txt)
     */
    suspend fun initializeSampleData() {
        val sampleProducts = listOf(
            Product(
                name = "Chocolate Cake",
                description = "Rich chocolate cake with cream frosting",
                category = ProductCategory.CAKES,
                sellingPrice = 450.0,
                costPrice = 280.0,
                servings = 8,
                isPopular = true
            ),
            Product(
                name = "Vanilla Cupcakes",
                description = "Soft vanilla cupcakes with buttercream",
                category = ProductCategory.CAKES,
                sellingPrice = 60.0,
                costPrice = 35.0,
                servings = 1,
                isPopular = true
            ),
            Product(
                name = "Croissants",
                description = "Buttery, flaky French pastries",
                category = ProductCategory.PASTRIES,
                sellingPrice = 80.0,
                costPrice = 45.0,
                servings = 1,
                isPopular = true
            ),
            Product(
                name = "Sourdough Bread",
                description = "Artisan sourdough bread loaf",
                category = ProductCategory.BREADS,
                sellingPrice = 120.0,
                costPrice = 70.0,
                servings = 12
            ),
            Product(
                name = "Blueberry Muffins",
                description = "Fresh blueberry muffins",
                category = ProductCategory.PASTRIES,
                sellingPrice = 70.0,
                costPrice = 40.0,
                servings = 1
            ),
            Product(
                name = "Espresso",
                description = "Strong Italian coffee",
                category = ProductCategory.BEVERAGES,
                sellingPrice = 45.0,
                costPrice = 15.0,
                servings = 1,
                isPopular = true
            ),
            Product(
                name = "Cappuccino",
                description = "Espresso with steamed milk foam",
                category = ProductCategory.BEVERAGES,
                sellingPrice = 65.0,
                costPrice = 25.0,
                servings = 1
            ),
            Product(
                name = "Red Velvet Cake",
                description = "Classic red velvet with cream cheese frosting",
                category = ProductCategory.CAKES,
                sellingPrice = 520.0,
                costPrice = 320.0,
                servings = 8
            )
        )

        sampleProducts.forEach { product ->
            productDao.insertProduct(product)
        }
    }
    
    /**
     * Get products by category
     */
    fun getProductsByCategory(category: String): Flow<List<Product>> = 
        productDao.getProductsByCategory(category)
    
    /**
     * Get all categories
     */
    fun getAllCategories(): Flow<List<String>> = productDao.getAllCategories()
    
    /**
     * Get product by ID
     */
    suspend fun getProductById(id: Long): Product? = productDao.getProductById(id)
    
    /**
     * Get product by name
     */
    suspend fun getProductByName(name: String): Product? = productDao.getProductByName(name)
    
    /**
     * Search products by name
     */
    fun searchProducts(searchQuery: String): Flow<List<Product>> = 
        productDao.searchProducts(searchQuery)
    
    /**
     * Insert a new product
     */
    suspend fun insertProduct(product: Product): Long = productDao.insertProduct(product)
    
    /**
     * Insert multiple products
     */
    suspend fun insertProducts(products: List<Product>): List<Long> = 
        productDao.insertProducts(products)
    
    /**
     * Update an existing product
     */
    suspend fun updateProduct(product: Product) = productDao.updateProduct(product)
    
    /**
     * Update product cost price
     */
    suspend fun updateProductCostPrice(id: Long, newCostPrice: Double) = 
        productDao.updateProductCostPrice(id, newCostPrice)
    
    /**
     * Update product selling price
     */
    suspend fun updateProductSellingPrice(id: Long, newSellingPrice: Double) = 
        productDao.updateProductSellingPrice(id, newSellingPrice)
    
    /**
     * Set manual cost override
     */
    suspend fun setManualCostOverride(id: Long, manualCost: Double?) = 
        productDao.setManualCostOverride(id, manualCost)
    
    /**
     * Toggle product active status
     */
    suspend fun toggleProductActiveStatus(id: Long, isActive: Boolean) = 
        productDao.toggleProductActiveStatus(id, isActive)
    
    /**
     * Delete a product
     */
    suspend fun deleteProduct(product: Product) = productDao.deleteProduct(product)
    
    /**
     * Delete product by ID
     */
    suspend fun deleteProductById(id: Long) = productDao.deleteProductById(id)
    
    /**
     * Get total count of active products
     */
    suspend fun getActiveProductCount(): Int = productDao.getActiveProductCount()
    
    /**
     * Get products with highest profit margin
     */
    suspend fun getTopProfitableProducts(limit: Int = 10): List<Product> = 
        productDao.getTopProfitableProducts(limit)
    
    /**
     * Calculate and update product cost based on recipe ingredients
     */
    suspend fun calculateAndUpdateProductCost(productId: Long, ingredientCosts: Map<Long, Double>) {
        // This would involve getting the recipe for the product and calculating total cost
        // Implementation would depend on recipe repository
        // For now, this is a placeholder
    }
    
    /**
     * Get products that need price updates
     */
    suspend fun getProductsNeedingPriceUpdate(): List<Product> {
        // Products where cost price might be outdated compared to ingredient prices
        return emptyList() // Placeholder implementation
    }
}
