package com.bakery.tracker.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import androidx.room.Room
import com.bakery.tracker.data.local.BakeryDatabase
import com.bakery.tracker.data.local.dao.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing application-level dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    
    /**
     * Provides the Room database instance
     */
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): BakeryDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            BakeryDatabase::class.java,
            "bakery_database"
        )
            .fallbackToDestructiveMigration()
            .build()
    }
    
    /**
     * Provides IngredientDao
     */
    @Provides
    fun provideIngredientDao(database: BakeryDatabase): IngredientDao {
        return database.ingredientDao()
    }
    
    /**
     * Provides ProductDao
     */
    @Provides
    fun provideProductDao(database: BakeryDatabase): ProductDao {
        return database.productDao()
    }
    
    /**
     * Provides RecipeDao
     */
    @Provides
    fun provideRecipeDao(database: BakeryDatabase): RecipeDao {
        return database.recipeDao()
    }
    
    /**
     * Provides ProductionDao
     */
    @Provides
    fun provideProductionDao(database: BakeryDatabase): ProductionDao {
        return database.productionDao()
    }
    
    /**
     * Provides BillDao
     */
    @Provides
    fun provideBillDao(database: BakeryDatabase): BillDao {
        return database.billDao()
    }

    /**
     * Provides CartDao
     */
    @Provides
    fun provideCartDao(database: BakeryDatabase): CartDao {
        return database.cartDao()
    }
    
    /**
     * Provides DataStore for preferences
     */
    @Provides
    @Singleton
    fun provideDataStore(@ApplicationContext context: Context): DataStore<Preferences> {
        return context.dataStore
    }
}

/**
 * Extension property for DataStore
 */
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")
