# 🐛 Debugging Guide - Bakery Tracker Pro

## Quick Debugging Checklist

### 1. **Build Issues**

If you encounter build errors, check these common issues:

#### **Gradle Wrapper Missing**
```bash
# If gradlew is not working, you may need to regenerate the wrapper
# In Android Studio: File > New > Project... then copy gradle wrapper files
```

#### **Dependency Issues**
```bash
# Clean and rebuild
./gradlew clean
./gradlew :app:assembleDebug
```

#### **Kotlin Version Conflicts**
- Ensure all Kotlin versions match in `build.gradle.kts`
- Check Compose compiler version compatibility

### 2. **Runtime Issues**

#### **Hilt Injection Errors**
- Ensure `@HiltAndroidApp` is on the Application class
- Check all `@Inject` constructors are properly annotated
- Verify `@AndroidEntryPoint` is on Activities/Fragments

#### **Room Database Issues**
- Check entity relationships and foreign keys
- Verify DAO query syntax
- Ensure database version is incremented for schema changes

#### **Navigation Issues**
- Verify all screen routes are defined
- Check NavHost startDestination exists
- Ensure proper navigation argument passing

### 3. **UI Issues**

#### **Compose Preview Not Working**
- Check if all preview functions have `@Preview` annotation
- Ensure preview functions don't use ViewModels with dependencies
- Verify theme is properly applied

#### **Dark Mode Not Persisting**
- Check DataStore implementation in `PreferencesManager`
- Verify theme state collection in `ThemeViewModel`
- Ensure proper StateFlow usage

### 4. **Data Issues**

#### **Database Not Creating**
- Check Room database configuration
- Verify entity annotations
- Ensure proper database initialization in Hilt module

#### **StateFlow Not Updating**
- Check if StateFlow is properly collected with `collectAsState()`
- Verify ViewModel scope and lifecycle
- Ensure proper state updates in repositories

## 🔍 Debugging Tools

### Android Studio Debugging
1. **Logcat**: Monitor app logs and crashes
2. **Database Inspector**: View Room database contents
3. **Layout Inspector**: Debug Compose UI hierarchy
4. **Network Inspector**: Monitor API calls (future feature)

### Code Quality Tools
```bash
# Run all quality checks
./gradlew ktlintCheck detekt testDebug

# Fix formatting issues
./gradlew ktlintFormat

# Run specific tests
./gradlew test --tests="*IngredientCalculatorTest*"
```

## 🚨 Common Error Messages

### **"Cannot resolve symbol 'hiltViewModel'"**
**Solution**: Add Hilt navigation compose dependency
```kotlin
implementation("androidx.hilt:hilt-navigation-compose:1.2.0")
```

### **"Unresolved reference: collectAsState"**
**Solution**: Add compose runtime dependency
```kotlin
implementation("androidx.compose.runtime:runtime:$compose_version")
```

### **"Room cannot find implementation"**
**Solution**: Ensure kapt is properly configured
```kotlin
kapt("androidx.room:room-compiler:2.6.1")
```

### **"DataStore not found"**
**Solution**: Check DataStore dependencies and imports
```kotlin
implementation("androidx.datastore:datastore-preferences:1.1.0")
```

## 🧪 Testing Debugging

### Unit Test Issues
- Ensure MockK is properly configured
- Check test dependencies in `build.gradle.kts`
- Verify coroutine test rules

### UI Test Issues
- Check Compose test dependencies
- Ensure proper test annotations
- Verify Hilt test setup

## 📱 Device/Emulator Issues

### **App Crashes on Launch**
1. Check Logcat for stack traces
2. Verify minimum SDK version (24+)
3. Check permissions in manifest
4. Ensure proper Hilt setup

### **Dark Mode Not Working**
1. Check device system settings
2. Verify theme implementation
3. Test DataStore persistence

### **Navigation Not Working**
1. Check bottom navigation visibility
2. Verify user role permissions
3. Test login/logout flow

## 🔧 Quick Fixes

### Reset App State
```bash
# Clear app data
adb shell pm clear com.bakery.tracker
```

### Force Gradle Sync
```bash
# In Android Studio
File > Sync Project with Gradle Files
```

### Clean Build
```bash
./gradlew clean
./gradlew build
```

## 📋 Verification Steps

After fixing issues, verify these work:

1. ✅ App builds successfully
2. ✅ App launches without crashes
3. ✅ Login screen appears
4. ✅ Dark mode toggle works
5. ✅ Navigation between screens works
6. ✅ Dashboard shows mock data
7. ✅ Role switching works (Admin/Staff)
8. ✅ Settings screen functions properly

## 🆘 Getting Help

If issues persist:

1. **Check Android Studio Build Output** for detailed error messages
2. **Review Logcat** for runtime errors
3. **Verify Dependencies** are up to date
4. **Check Kotlin/Compose Compatibility** matrix
5. **Test on Different Devices/Emulators**

## 📚 Useful Resources

- [Jetpack Compose Documentation](https://developer.android.com/jetpack/compose)
- [Hilt Documentation](https://dagger.dev/hilt/)
- [Room Documentation](https://developer.android.com/training/data-storage/room)
- [Navigation Compose](https://developer.android.com/jetpack/compose/navigation)
- [Material 3 Design](https://m3.material.io/)

---

**Remember**: Most issues are related to dependency versions, missing annotations, or incorrect imports. Start with the basics and work your way up! 🚀
