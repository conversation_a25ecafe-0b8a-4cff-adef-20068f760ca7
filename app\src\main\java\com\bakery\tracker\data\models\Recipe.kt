package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Represents a recipe for a bakery product.
 * As described in overview.txt - Recipe Management features
 *
 * @param id Unique identifier for the recipe
 * @param productId ID of the product this recipe creates
 * @param name Name of the recipe
 * @param instructions Step-by-step cooking instructions
 * @param servings Number of servings this recipe produces
 * @param prepTime Preparation time in minutes
 * @param cookTime Cooking/baking time in minutes
 * @param difficulty Recipe difficulty level
 * @param category Recipe category (matches product category)
 * @param imageUrl URL or path to recipe image
 * @param isActive Whether this recipe is currently in use
 * @param createdAt Timestamp when recipe was created
 * @param updatedAt Timestamp when recipe was last updated
 */
@Entity(tableName = "recipes")
data class Recipe(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val productId: Long,
    val name: String,
    val instructions: String = "",
    val servings: Int,
    val prepTime: Int = 0,
    val cookTime: Int = 0,
    val difficulty: RecipeDifficulty = RecipeDifficulty.MEDIUM,
    val category: ProductCategory,
    val imageUrl: String = "",
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * Calculates total time required for this recipe (prep + cook time)
     */
    fun getTotalTimeMinutes(): Int = prepTime + cookTime

    /**
     * Formats total time as a readable string
     */
    fun getFormattedTotalTime(): String {
        val totalMinutes = getTotalTimeMinutes()
        val hours = totalMinutes / 60
        val minutes = totalMinutes % 60

        return when {
            hours > 0 && minutes > 0 -> "${hours}h ${minutes}m"
            hours > 0 -> "${hours}h"
            minutes > 0 -> "${minutes}m"
            else -> "0m"
        }
    }

    /**
     * Gets difficulty display name
     */
    fun getDifficultyDisplayName(): String = difficulty.displayName
}

/**
 * Recipe difficulty levels as mentioned in overview.txt
 */
enum class RecipeDifficulty(val displayName: String) {
    EASY("Easy"),
    MEDIUM("Medium"),
    HARD("Hard")
}
