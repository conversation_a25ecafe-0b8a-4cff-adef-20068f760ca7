package com.bakery.tracker.util

import com.bakery.tracker.data.models.Ingredient
import com.bakery.tracker.data.models.RecipeIngredient
import kotlin.math.ceil

/**
 * Utility class for calculating ingredient requirements and costs
 */
object IngredientCalculator {
    
    /**
     * Calculate ingredient requirements for a given recipe and quantity
     * 
     * @param recipeIngredients List of ingredients in the recipe
     * @param batchSize Original batch size of the recipe
     * @param desiredQuantity Desired quantity to produce
     * @return Map of ingredient ID to required quantity
     */
    fun calculateIngredientRequirements(
        recipeIngredients: List<RecipeIngredient>,
        batchSize: Int,
        desiredQuantity: Int
    ): Map<Long, Double> {
        val scalingFactor = desiredQuantity.toDouble() / batchSize.toDouble()
        
        return recipeIngredients.associate { recipeIngredient ->
            recipeIngredient.ingredientId to (recipeIngredient.quantity * scalingFactor)
        }
    }
    
    /**
     * Calculate the total cost of a recipe based on current ingredient prices
     * 
     * @param recipeIngredients List of recipe ingredients with details
     * @param batchSize Original batch size of the recipe
     * @param desiredQuantity Desired quantity to produce
     * @return Total cost for the desired quantity
     */
    fun calculateRecipeCost(
        recipeIngredients: List<RecipeIngredientWithPrice>,
        batchSize: Int,
        desiredQuantity: Int
    ): Double {
        val scalingFactor = desiredQuantity.toDouble() / batchSize.toDouble()
        
        return recipeIngredients.sumOf { ingredient ->
            val requiredQuantity = ingredient.quantity * scalingFactor
            requiredQuantity * ingredient.pricePerUnit
        }
    }
    
    /**
     * Calculate cost per unit for a recipe
     * 
     * @param totalCost Total cost of the recipe
     * @param quantity Quantity produced
     * @return Cost per unit
     */
    fun calculateCostPerUnit(totalCost: Double, quantity: Int): Double {
        return if (quantity > 0) totalCost / quantity else 0.0
    }
    
    /**
     * Check if there's sufficient stock for all ingredients
     * 
     * @param requirements Map of ingredient ID to required quantity
     * @param availableStock Map of ingredient ID to available stock
     * @return True if all ingredients have sufficient stock
     */
    fun hasSufficientStock(
        requirements: Map<Long, Double>,
        availableStock: Map<Long, Double>
    ): Boolean {
        return requirements.all { (ingredientId, requiredQuantity) ->
            val available = availableStock[ingredientId] ?: 0.0
            available >= requiredQuantity
        }
    }
    
    /**
     * Get missing ingredients (insufficient stock)
     * 
     * @param requirements Map of ingredient ID to required quantity
     * @param availableStock Map of ingredient ID to available stock
     * @return Map of ingredient ID to shortage amount
     */
    fun getMissingIngredients(
        requirements: Map<Long, Double>,
        availableStock: Map<Long, Double>
    ): Map<Long, Double> {
        return requirements.mapNotNull { (ingredientId, requiredQuantity) ->
            val available = availableStock[ingredientId] ?: 0.0
            val shortage = requiredQuantity - available
            if (shortage > 0) {
                ingredientId to shortage
            } else {
                null
            }
        }.toMap()
    }
    
    /**
     * Calculate maximum possible batches with current stock
     * 
     * @param recipeIngredients List of ingredients in the recipe
     * @param availableStock Map of ingredient ID to available stock
     * @return Maximum number of complete batches possible
     */
    fun calculateMaxPossibleBatches(
        recipeIngredients: List<RecipeIngredient>,
        availableStock: Map<Long, Double>
    ): Int {
        if (recipeIngredients.isEmpty()) return 0
        
        return recipeIngredients.minOfOrNull { ingredient ->
            val available = availableStock[ingredient.ingredientId] ?: 0.0
            if (ingredient.quantity > 0) {
                (available / ingredient.quantity).toInt()
            } else {
                Int.MAX_VALUE
            }
        } ?: 0
    }
    
    /**
     * Convert units if needed (basic conversion)
     * This is a simplified version - in a real app you'd have a comprehensive unit conversion system
     * 
     * @param quantity Original quantity
     * @param fromUnit Original unit
     * @param toUnit Target unit
     * @return Converted quantity
     */
    fun convertUnits(quantity: Double, fromUnit: String, toUnit: String): Double {
        // Simplified conversion - in reality you'd have a proper conversion table
        return when {
            fromUnit.equals(toUnit, ignoreCase = true) -> quantity
            fromUnit.equals("kg", ignoreCase = true) && toUnit.equals("g", ignoreCase = true) -> quantity * 1000
            fromUnit.equals("g", ignoreCase = true) && toUnit.equals("kg", ignoreCase = true) -> quantity / 1000
            fromUnit.equals("l", ignoreCase = true) && toUnit.equals("ml", ignoreCase = true) -> quantity * 1000
            fromUnit.equals("ml", ignoreCase = true) && toUnit.equals("l", ignoreCase = true) -> quantity / 1000
            else -> quantity // No conversion available, return original
        }
    }
    
    /**
     * Calculate reorder quantity based on usage patterns
     * 
     * @param currentStock Current stock level
     * @param lowStockThreshold Minimum stock threshold
     * @param averageDailyUsage Average daily usage
     * @param leadTimeDays Lead time for restocking in days
     * @return Suggested reorder quantity
     */
    fun calculateReorderQuantity(
        currentStock: Double,
        lowStockThreshold: Double,
        averageDailyUsage: Double,
        leadTimeDays: Int = 7
    ): Double {
        val safetyStock = averageDailyUsage * leadTimeDays
        val reorderPoint = lowStockThreshold + safetyStock
        
        return if (currentStock <= reorderPoint) {
            // Calculate quantity to bring stock to a comfortable level
            val targetStock = reorderPoint + (averageDailyUsage * 14) // 2 weeks buffer
            maxOf(0.0, targetStock - currentStock)
        } else {
            0.0
        }
    }
}

/**
 * Data class for recipe ingredient with price information
 */
data class RecipeIngredientWithPrice(
    val ingredientId: Long,
    val quantity: Double,
    val unit: String,
    val pricePerUnit: Double,
    val ingredientName: String
)
