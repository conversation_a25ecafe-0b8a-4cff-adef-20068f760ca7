package com.bakery.tracker.domain.usecase

import com.bakery.tracker.data.models.Recipe
import com.bakery.tracker.data.models.RecipeIngredient
import com.bakery.tracker.data.repository.RecipeRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Use case for adding a new recipe with ingredients
 */
class AddRecipeUseCase @Inject constructor(
    private val recipeRepository: RecipeRepository
) {
    
    /**
     * Execute the use case to add a recipe
     * 
     * @param recipe The recipe to add
     * @param ingredients List of ingredients for the recipe
     * @return Result containing the recipe ID or error
     */
    suspend operator fun invoke(
        recipe: Recipe,
        ingredients: List<RecipeIngredient>
    ): Result<Long> = withContext(Dispatchers.IO) {
        try {
            // Validate recipe data
            validateRecipe(recipe, ingredients)
            
            // Insert recipe with ingredients in a transaction
            val recipeId = recipeRepository.insertRecipeWithIngredients(recipe, ingredients)
            
            Result.success(recipeId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Validate recipe data before insertion
     */
    private fun validateRecipe(recipe: Recipe, ingredients: List<RecipeIngredient>) {
        require(recipe.name.isNotBlank()) { "Recipe name cannot be empty" }
        require(recipe.batchSize > 0) { "Batch size must be greater than 0" }
        require(ingredients.isNotEmpty()) { "Recipe must have at least one ingredient" }
        
        ingredients.forEach { ingredient ->
            require(ingredient.quantity > 0) { "Ingredient quantity must be greater than 0" }
            require(ingredient.unit.isNotBlank()) { "Ingredient unit cannot be empty" }
        }
    }
}
