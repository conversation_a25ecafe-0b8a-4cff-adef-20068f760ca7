package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * Represents the relationship between a recipe and its ingredients with quantities.
 * 
 * @param id Unique identifier for this recipe-ingredient relationship
 * @param recipeId ID of the recipe
 * @param ingredientId ID of the ingredient
 * @param quantity Quantity of ingredient needed for this recipe
 * @param unit Unit of measurement for this ingredient in the recipe
 * @param notes Optional notes about this ingredient in the recipe
 */
@Entity(
    tableName = "recipe_ingredients",
    foreignKeys = [
        ForeignKey(
            entity = Recipe::class,
            parentColumns = ["id"],
            childColumns = ["recipeId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = Ingredient::class,
            parentColumns = ["id"],
            childColumns = ["ingredientId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["recipeId"]),
        Index(value = ["ingredientId"]),
        Index(value = ["recipeId", "ingredientId"], unique = true)
    ]
)
data class RecipeIngredient(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val recipeId: Long,
    val ingredientId: Long,
    val quantity: Double,
    val unit: String,
    val notes: String = ""
)
