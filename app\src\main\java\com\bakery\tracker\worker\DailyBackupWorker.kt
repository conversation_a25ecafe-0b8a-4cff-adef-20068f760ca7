package com.bakery.tracker.worker

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.bakery.tracker.data.repository.IngredientRepository
import com.bakery.tracker.util.CsvUtil
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * WorkManager worker for daily backup operations
 */
@HiltWorker
class DailyBackupWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val ingredientRepository: IngredientRepository,
    private val csvUtil: CsvUtil
) : CoroutineWorker(context, workerParams) {
    
    companion object {
        const val WORK_NAME = "daily_backup_work"
        const val BACKUP_FOLDER = "BakeryTracker"
    }
    
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            // Create backup directory
            val backupDir = createBackupDirectory()
            
            // Generate backup filename with current date
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val currentDate = dateFormat.format(Date())
            val backupFileName = "backup-$currentDate"
            
            // Export data to CSV files
            exportDataToCsv(backupDir, backupFileName)
            
            // Create zip file (simplified - in real implementation you'd use proper zip library)
            createBackupZip(backupDir, backupFileName)
            
            // Clean up old backups (keep last 30 days)
            cleanupOldBackups(backupDir)
            
            Result.success()
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure()
        }
    }
    
    /**
     * Create backup directory in Documents
     */
    private fun createBackupDirectory(): File {
        val documentsDir = File(applicationContext.getExternalFilesDir(null), "Documents")
        val backupDir = File(documentsDir, BACKUP_FOLDER)
        
        if (!backupDir.exists()) {
            backupDir.mkdirs()
        }
        
        return backupDir
    }
    
    /**
     * Export data to CSV files
     */
    private suspend fun exportDataToCsv(backupDir: File, backupFileName: String) {
        // Export ingredients
        val ingredientsFile = File(backupDir, "${backupFileName}_ingredients.csv")
        csvUtil.exportIngredientsToCsv(ingredientsFile)
        
        // Export production logs (placeholder)
        val productionFile = File(backupDir, "${backupFileName}_production.csv")
        csvUtil.exportProductionToCsv(productionFile)
        
        // Export bills (placeholder)
        val billsFile = File(backupDir, "${backupFileName}_bills.csv")
        csvUtil.exportBillsToCsv(billsFile)
    }
    
    /**
     * Create backup zip file (simplified implementation)
     */
    private fun createBackupZip(backupDir: File, backupFileName: String) {
        // In a real implementation, you would use a proper zip library
        // For now, this is a placeholder
        val zipFile = File(backupDir, "$backupFileName.zip")
        
        // Create a simple marker file to indicate backup completion
        val markerFile = File(backupDir, "$backupFileName.backup")
        markerFile.writeText("Backup completed at ${Date()}")
    }
    
    /**
     * Clean up old backup files
     */
    private fun cleanupOldBackups(backupDir: File) {
        val cutoffTime = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000L) // 30 days
        
        backupDir.listFiles()?.forEach { file ->
            if (file.lastModified() < cutoffTime) {
                file.delete()
            }
        }
    }
}
