package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Represents a bill/invoice for customer orders.
 * 
 * @param id Unique identifier for the bill
 * @param billNumber Human-readable bill number
 * @param customerName Name of the customer
 * @param customerPhone Customer phone number
 * @param customerEmail Customer email address
 * @param subtotal Subtotal before discount and tax
 * @param discountAmount Discount amount applied
 * @param discountPercentage Discount percentage applied
 * @param taxAmount Tax amount applied
 * @param totalAmount Final total amount
 * @param paymentMethod Payment method used
 * @param paymentStatus Payment status (PENDING, PAID, CANCELLED)
 * @param notes Optional notes for the bill
 * @param createdAt Timestamp when bill was created
 * @param paidAt Timestamp when bill was paid (if applicable)
 */
@Entity(tableName = "bills")
data class Bill(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val billNumber: String,
    val customerName: String,
    val customerPhone: String = "",
    val customerEmail: String = "",
    val subtotal: Double,
    val discountAmount: Double = 0.0,
    val discountPercentage: Double = 0.0,
    val taxAmount: Double = 0.0,
    val totalAmount: Double,
    val paymentMethod: String = "CASH",
    val paymentStatus: PaymentStatus = PaymentStatus.PENDING,
    val notes: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val paidAt: Long? = null
) {
    /**
     * Formats the creation date as a readable string
     */
    fun getFormattedCreatedDate(): String {
        val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
        return dateFormat.format(Date(createdAt))
    }
    
    /**
     * Formats the paid date as a readable string
     */
    fun getFormattedPaidDate(): String? {
        return paidAt?.let {
            val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
            dateFormat.format(Date(it))
        }
    }
    
    /**
     * Checks if the bill is paid
     */
    fun isPaid(): Boolean = paymentStatus == PaymentStatus.PAID
    
    /**
     * Checks if the bill is pending
     */
    fun isPending(): Boolean = paymentStatus == PaymentStatus.PENDING
    
    /**
     * Checks if the bill is cancelled
     */
    fun isCancelled(): Boolean = paymentStatus == PaymentStatus.CANCELLED
}

/**
 * Enum representing different payment statuses
 */
enum class PaymentStatus {
    PENDING,
    PAID,
    CANCELLED
}
