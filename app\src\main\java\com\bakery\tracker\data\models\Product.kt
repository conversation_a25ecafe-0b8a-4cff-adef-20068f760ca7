package com.bakery.tracker.data.models

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Represents a bakery product that can be produced and sold.
 * Categories: Cakes, Pastries, Breads, Beverages (as per overview.txt)
 *
 * @param id Unique identifier for the product
 * @param name Name of the product (e.g., "Chocolate Cake", "White Bread")
 * @param description Description of the product
 * @param category Category: "Cakes", "Pastries", "Breads", "Beverages"
 * @param sellingPrice Price at which the product is sold (₹)
 * @param costPrice Calculated cost price based on ingredients (₹)
 * @param servings Number of servings this recipe produces
 * @param isPopular Whether this is marked as a popular item
 * @param imageUrl URL or path to product image
 * @param isActive Whether the product is currently being produced
 * @param createdAt Timestamp when product was added
 * @param updatedAt Timestamp when product was last updated
 */
@Entity(tableName = "products")
data class Product(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val description: String = "",
    val category: ProductCategory,
    val sellingPrice: Double,
    val costPrice: Double = 0.0,
    val servings: Int = 1,
    val isPopular: Boolean = false,
    val imageUrl: String = "",
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * Calculates profit margin percentage as per overview.txt formula
     */
    fun getProfitMargin(): Double {
        return if (sellingPrice > 0) {
            ((sellingPrice - costPrice) / sellingPrice) * 100
        } else {
            0.0
        }
    }

    /**
     * Calculates profit amount per unit
     */
    fun getProfitAmount(): Double = sellingPrice - costPrice

    /**
     * Calculates cost per serving
     */
    fun getCostPerServing(): Double = if (servings > 0) costPrice / servings else costPrice

    /**
     * Suggested pricing with different profit margins (30%, 40%, 50%)
     */
    fun getSuggestedPricing(): PricingSuggestions {
        return PricingSuggestions(
            margin30 = costPrice * 1.43, // 30% margin
            margin40 = costPrice * 1.67, // 40% margin
            margin50 = costPrice * 2.0   // 50% margin
        )
    }
}

/**
 * Product categories as defined in overview.txt
 */
enum class ProductCategory(val displayName: String) {
    CAKES("Cakes"),
    PASTRIES("Pastries"),
    BREADS("Breads"),
    BEVERAGES("Beverages")
}

/**
 * Pricing suggestions for different profit margins
 */
data class PricingSuggestions(
    val margin30: Double,
    val margin40: Double,
    val margin50: Double
)
